/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    PrimeAvatar: typeof import('primevue/avatar')['default']
    PrimeBadge: typeof import('primevue/badge')['default']
    PrimeButton: typeof import('primevue/button')['default']
    PrimeButtonGroup: typeof import('primevue/buttongroup')['default']
    PrimeCard: typeof import('primevue/card')['default']
    PrimeCheckbox: typeof import('primevue/checkbox')['default']
    PrimeChip: typeof import('primevue/chip')['default']
    PrimeColumn: typeof import('primevue/column')['default']
    PrimeDataTable: typeof import('primevue/datatable')['default']
    PrimeDialog: typeof import('primevue/dialog')['default']
    PrimeDivider: typeof import('primevue/divider')['default']
    PrimeFileUpload: typeof import('primevue/fileupload')['default']
    PrimeForm: typeof import('@primevue/forms/form')['default']
    PrimeInputNumber: typeof import('primevue/inputnumber')['default']
    PrimeInputText: typeof import('primevue/inputtext')['default']
    PrimeMessage: typeof import('primevue/message')['default']
    PrimeOverlayPanel: typeof import('primevue/overlaypanel')['default']
    PrimePaginator: typeof import('primevue/paginator')['default']
    PrimePassword: typeof import('primevue/password')['default']
    PrimePopover: typeof import('primevue/popover')['default']
    PrimeProgressBar: typeof import('primevue/progressbar')['default']
    PrimeProgressSpinner: typeof import('primevue/progressspinner')['default']
    PrimeRadioButton: typeof import('primevue/radiobutton')['default']
    PrimeSelect: typeof import('primevue/select')['default']
    PrimeSelectButton: typeof import('primevue/selectbutton')['default']
    PrimeTab: typeof import('primevue/tab')['default']
    PrimeTabList: typeof import('primevue/tablist')['default']
    PrimeTabs: typeof import('primevue/tabs')['default']
    PrimeTextarea: typeof import('primevue/textarea')['default']
    PrimeToast: typeof import('primevue/toast')['default']
    PrimeToggleButton: typeof import('primevue/togglebutton')['default']
    PrimeToggleSwitch: typeof import('primevue/toggleswitch')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
  export interface ComponentCustomProperties {
    Ripple: typeof import('primevue/ripple')['default']
    Tooltip: typeof import('primevue/tooltip')['default']
  }
}
