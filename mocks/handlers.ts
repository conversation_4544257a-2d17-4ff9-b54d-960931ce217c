import { http, HttpResponse, delay } from 'msw';
import { User } from '~/types/User';
import type { UserProfile } from '~/types/User';
import type {
  TranscriptionProgress,
  TranscriptionSettings
} from '~/types/Transcription';
import { Note } from '~/types/Note';

// Mock database of users
const mockUsers = [
  new User(
    1,
    'Alice Wonderland',
    'AW',
    500,
    200,
    'Premium',
    {
      firstName: 'Alice',
      lastName: 'Wonderland',
      businessName: 'Alice Wonderland',
      phoneNumber: '1234567890',
      email: '<EMAIL>'
    } as UserProfile,
    '<EMAIL>'
  ),
  new User(
    2,
    '<PERSON> Dylan',
    'BD',
    300,
    25,
    'Free',
    {
      firstName: 'Bob',
      lastName: 'Dylan',
      businessName: 'Bob Dylan',
      phoneNumber: '0987654321',
      email: '<EMAIL>'
    } as UserProfile,
    '<EMAIL>'
  )
];

// Mock authentication tokens
const mockTokens = new Map<string, number>();
mockTokens.set('mock-token-1', 1);
mockTokens.set('mock-token-2', 2);

// Mock notes database
const mockNotes = new Map<number, Note[]>();
// Initialize mock notes for users
mockNotes.set(1, [
  new Note(
    1,
    'Gemeindesitzung Meeting',
    new Date('2025-07-15'),
    '45:30',
    'Gemeindesitzungen',
    'completed',
    1,
    {
      fileId: 'file-1',
      transcriptionId: 'trans-1',
      fileUrl: '/mock-audio-1.mp3',
      fileSize: 2500000,
      fileType: 'audio/mpeg',
      createdAt: new Date('2025-01-15T10:00:00Z'),
      updatedAt: new Date('2025-01-15T10:45:00Z')
    }
  ),
  new Note(
    2,
    'Marketing Strategy Discussion',
    new Date('2025-06-16'),
    '32:15',
    'Marketing',
    'completed',
    1,
    {
      fileId: 'file-2',
      transcriptionId: 'trans-2',
      fileUrl: '/mock-audio-2.mp3',
      fileSize: 1800000,
      fileType: 'audio/mpeg',
      createdAt: new Date('2025-01-20T14:00:00Z'),
      updatedAt: new Date('2025-01-20T14:32:00Z')
    }
  ),
  new Note(
    3,
    'Team Standup',
    new Date('2025-06-28'),
    '15:45',
    null,
    'processing',
    1,
    {
      fileId: 'file-3',
      transcriptionId: 'trans-3',
      fileUrl: '/mock-audio-3.mp3',
      fileSize: 900000,
      fileType: 'audio/mpeg',
      createdAt: new Date('2025-01-22T09:00:00Z'),
      updatedAt: new Date('2025-01-22T09:15:00Z')
    }
  ),
  new Note(
    11,
    'Gemeindesitzung Meeting',
    new Date('2025-07-01'),
    '45:30',
    'Gemeindesitzungen',
    'completed',
    1,
    {
      fileId: 'file-1',
      transcriptionId: 'trans-1',
      fileUrl: '/mock-audio-1.mp3',
      fileSize: 2500000,
      fileType: 'audio/mpeg',
      createdAt: new Date('2025-01-15T10:00:00Z'),
      updatedAt: new Date('2025-01-15T10:45:00Z')
    }
  ),
  new Note(
    12,
    'Marketing Strategy Discussion',
    new Date('2025-07-02'),
    '32:15',
    'Marketing',
    'completed',
    1,
    {
      fileId: 'file-2',
      transcriptionId: 'trans-2',
      fileUrl: '/mock-audio-2.mp3',
      fileSize: 1800000,
      fileType: 'audio/mpeg',
      createdAt: new Date('2025-01-20T14:00:00Z'),
      updatedAt: new Date('2025-01-20T14:32:00Z')
    }
  ),
  new Note(
    13,
    'Team Standup',
    new Date('2025-07-15'),
    '15:45',
    null,
    'processing',
    1,
    {
      fileId: 'file-3',
      transcriptionId: 'trans-3',
      fileUrl: '/mock-audio-3.mp3',
      fileSize: 900000,
      fileType: 'audio/mpeg',
      createdAt: new Date('2025-01-22T09:00:00Z'),
      updatedAt: new Date('2025-01-22T09:15:00Z')
    }
  ),
  new Note(
    21,
    'Gemeindesitzung Meeting',
    new Date('2025-07-15'),
    '45:30',
    'Gemeindesitzungen',
    'completed',
    1,
    {
      fileId: 'file-1',
      transcriptionId: 'trans-1',
      fileUrl: '/mock-audio-1.mp3',
      fileSize: 2500000,
      fileType: 'audio/mpeg',
      createdAt: new Date('2025-01-15T10:00:00Z'),
      updatedAt: new Date('2025-01-15T10:45:00Z')
    }
  ),
  new Note(
    22,
    'Marketing Strategy Discussion',
    new Date('2025-07-18'),
    '32:15',
    'Marketing',
    'completed',
    1,
    {
      fileId: 'file-2',
      transcriptionId: 'trans-2',
      fileUrl: '/mock-audio-2.mp3',
      fileSize: 1800000,
      fileType: 'audio/mpeg',
      createdAt: new Date('2025-01-20T14:00:00Z'),
      updatedAt: new Date('2025-01-20T14:32:00Z')
    }
  ),
  new Note(
    23,
    'Team Standup',
    new Date('2025-07-22'),
    '15:45',
    null,
    'processing',
    1,
    {
      fileId: 'file-3',
      transcriptionId: 'trans-3',
      fileUrl: '/mock-audio-3.mp3',
      fileSize: 900000,
      fileType: 'audio/mpeg',
      createdAt: new Date('2025-01-22T09:00:00Z'),
      updatedAt: new Date('2025-01-22T09:15:00Z')
    }
  ),
  new Note(
    31,
    'Gemeindesitzung Meeting',
    new Date('2025-07-23'),
    '45:30',
    'Gemeindesitzungen',
    'completed',
    31,
    {
      fileId: 'file-1',
      transcriptionId: 'trans-1',
      fileUrl: '/mock-audio-1.mp3',
      fileSize: 2500000,
      fileType: 'audio/mpeg',
      createdAt: new Date('2025-01-15T10:00:00Z'),
      updatedAt: new Date('2025-01-15T10:45:00Z')
    }
  ),
  new Note(
    32,
    'Marketing Strategy Discussion',
    new Date('2025-07-23'),
    '32:15',
    'Marketing',
    'completed',
    1,
    {
      fileId: 'file-2',
      transcriptionId: 'trans-2',
      fileUrl: '/mock-audio-2.mp3',
      fileSize: 1800000,
      fileType: 'audio/mpeg',
      createdAt: new Date('2025-01-20T14:00:00Z'),
      updatedAt: new Date('2025-01-20T14:32:00Z')
    }
  ),
  new Note(
    33,
    'Team Standup',
    new Date('2025-07-23'),
    '15:45',
    null,
    'processing',
    1,
    {
      fileId: 'file-3',
      transcriptionId: 'trans-3',
      fileUrl: '/mock-audio-3.mp3',
      fileSize: 900000,
      fileType: 'audio/mpeg',
      createdAt: new Date('2025-01-22T09:00:00Z'),
      updatedAt: new Date('2025-01-22T09:15:00Z')
    }
  )
]);

mockNotes.set(2, [
  new Note(
    4,
    'Client Call Recording',
    new Date('2025-01-18'),
    '28:20',
    'Client Calls',
    'completed',
    2,
    {
      fileId: 'file-4',
      transcriptionId: 'trans-4',
      fileUrl: '/mock-audio-4.mp3',
      fileSize: 1600000,
      fileType: 'audio/mpeg',
      createdAt: new Date('2025-01-18T11:00:00Z'),
      updatedAt: new Date('2025-01-18T11:28:00Z')
    }
  ),
  new Note(
    5,
    'Failed Note',
    new Date('2025-01-20'),
    '28:20',
    'Client Calls',
    'failed',
    2,
    {
      fileId: 'file-4',
      transcriptionId: 'trans-4',
      fileUrl: '/mock-audio-4.mp3',
      fileSize: 1600000,
      fileType: 'audio/mpeg',
      createdAt: new Date('2025-01-18T11:00:00Z'),
      updatedAt: new Date('2025-01-18T11:28:00Z')
    }
  )
]);

// Mock notes database
const mockFolders = new Map<number, string[]>();

mockFolders.set(1, ['Gemeindesitzungen', 'Marketing', 'Team Standup']);
mockFolders.set(2, ['Client Calls']);

// Generate a simple mock token
function generateToken(userId: number): string {
  const token = `mock-token-${userId}`;
  mockTokens.set(token, userId);
  return token;
}

// Validate token and return user ID
function validateToken(token: string): number | null {
  return mockTokens.get(token) || null;
}

// Mock transcription progress tracking
let mockTranscriptionStartTime: number | null = null;
let mockLastProgress = 0;

export const handlers = [
  http.get('/api/user/:id', ({ params }) => {
    const userId = Number(params.id);
    const user = mockUsers.find((u) => u.id === userId);

    if (user) {
      return HttpResponse.json({ user });
    } else {
      return HttpResponse.json({ error: 'User not found' }, { status: 404 });
    }
  }),

  http.post('/api/login', async ({ request }) => {
    const body = (await request.json().catch(() => ({}))) as {
      username?: string;
      password?: string;
    };

    // Mock authentication logic
    let user: User | undefined;
    if (body.username === 'admin' && body.password === 'admin') {
      user = mockUsers[0]; // Alice
    } else if (body.username === 'user' && body.password === 'user') {
      user = mockUsers[1]; // Bob
    }

    if (user) {
      const token = generateToken(user.id);
      return HttpResponse.json({
        success: true,
        user,
        token,
        message: 'Login successful'
      });
    }

    return HttpResponse.json(
      {
        success: false,
        message: 'Invalid username or password'
      },
      { status: 401 }
    );
  }),

  http.post('/api/logout', async ({ request }) => {
    const body = (await request.json().catch(() => ({}))) as {
      token?: string;
      id?: number;
    };

    // Remove token from mock storage
    if (body.token) {
      mockTokens.delete(body.token);
    }

    return HttpResponse.json({
      success: true,
      message: 'Logout successful'
    });
  }),

  http.post('/api/verify-token', async ({ request }) => {
    const body = (await request.json().catch(() => ({}))) as {
      token?: string;
    };

    if (!body.token) {
      return HttpResponse.json({ error: 'Token required' }, { status: 400 });
    }

    const userId = validateToken(body.token);
    if (userId) {
      const user = mockUsers.find((u) => u.id === userId);
      if (user) {
        return HttpResponse.json({ user });
      }
    }

    return HttpResponse.json({ error: 'Invalid token' }, { status: 401 });
  }),

  http.post('/api/support', async ({ request }) => {
    const body = await request.json();
    return HttpResponse.json({
      message: 'Successful Support Request',
      body: body
    });
  }),

  http.post('/api/note', async ({ request }) => {
    await delay(1000); // Simulate network delay

    const authHeader = request.headers.get('Authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return HttpResponse.json(
        { error: 'Authorization required' },
        { status: 401 }
      );
    }

    const userId = validateToken(token);
    if (!userId) {
      return HttpResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;
    const settingsStr = formData.get('settings') as string;

    if (!file) {
      return new HttpResponse(JSON.stringify({ error: 'No file provided' }), {
        status: 400
      });
    }

    let settings = {};
    try {
      settings = settingsStr ? JSON.parse(settingsStr) : {};
    } catch (error) {
      console.warn('Failed to parse settings:', error);
    }

    const fileId = 'file-' + Math.random().toString(36);
    const transcriptionId = 'transcription-' + Math.random().toString(36);
    const noteId = Date.now() + Math.floor(Math.random() * 1000);

    // Create a new note and add it to the user's notes
    const newNote = new Note(
      noteId,
      (settings as TranscriptionSettings)?.name ||
        file.name ||
        'Aufnahme-KI-note.mp3',
      new Date(),
      '07:00', // This would be calculated from the actual file
      (settings as TranscriptionSettings)?.folder || null,
      'processing',
      userId,
      {
        fileId,
        transcriptionId,
        fileUrl: URL.createObjectURL(file),
        fileSize: file.size || 75497472,
        fileType: file.type || 'audio/mpeg'
      }
    );

    // Add to mock database
    const userNotes = mockNotes.get(userId) || [];
    userNotes.push(newNote);
    mockNotes.set(userId, userNotes);

    // Initialize transcription progress tracking
    mockTranscriptionStartTime = Date.now();
    mockLastProgress = 0;

    // Mock successful response returning UploadedFile type
    return HttpResponse.json({
      file: file,
      name: newNote.name,
      size: newNote.fileSize,
      type: newNote.fileType,
      url: newNote.fileUrl,
      duration: newNote.duration,
      id: fileId,
      noteId: newNote.id,
      transcription: {
        id: transcriptionId,
        status: 'processing',
        estimatedCompletionTime: 5,
        message: 'File uploaded and transcription started successfully'
      }
    });
  }),

  http.get('/api/transcription/status', () => {
    return HttpResponse.json({
      isProcessing: false,
      fileId: null,
      progress: 0,
      estimatedTime: null
    });
  }),

  http.get('/api/transcription/progress', () => {
    // Simulate more realistic progress that gradually increases
    const now = Date.now();
    const startTime = mockTranscriptionStartTime || now;
    const elapsed = now - startTime;

    // Simulate 30 seconds total processing time
    const totalTime = 30000;
    let progress = Math.min(100, Math.floor((elapsed / totalTime) * 100));

    // Add some randomness but ensure progress always moves forward
    progress = Math.max(progress, mockLastProgress || 0);
    mockLastProgress = progress;

    const status = progress >= 100 ? 'completed' : 'processing';
    const estimatedTime =
      progress < 100
        ? Math.max(1, Math.ceil((totalTime - elapsed) / 1000))
        : null;

    return HttpResponse.json({
      progress: progress,
      status: status,
      estimatedTime: estimatedTime
    } as TranscriptionProgress);
  }),

  http.get('/api/download/pdf', () => {
    return HttpResponse.redirect('/144.pdf');
  }),

  http.get('/api/download/docx', () => {
    return HttpResponse.redirect('/144.docx');
  }),

  // Get user notes
  http.get('/api/notes', ({ request }) => {
    const authHeader = request.headers.get('Authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return HttpResponse.json(
        { error: 'Authorization required' },
        { status: 401 }
      );
    }

    const userId = validateToken(token);
    if (!userId) {
      return HttpResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    const userNotes = mockNotes.get(userId) || [];
    return HttpResponse.json({
      notes: userNotes.map((note) => note.toJSON())
    });
  }),

  // Get user notes
  http.get('/api/folders', ({ request }) => {
    const authHeader = request.headers.get('Authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return HttpResponse.json(
        { error: 'Authorization required' },
        { status: 401 }
      );
    }

    const userId = validateToken(token);
    if (!userId) {
      return HttpResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    const userFolders = mockFolders.get(userId) || [];
    return HttpResponse.json({
      folders: userFolders
    });
  }),

  http.post('/api/folder', async ({ request }) => {
    await delay(1000); // Simulate network delay

    const authHeader = request.headers.get('Authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return HttpResponse.json(
        { error: 'Authorization required' },
        { status: 401 }
      );
    }

    const userId = validateToken(token);
    if (!userId) {
      return HttpResponse.json({ error: 'Invalid token' }, { status: 401 });
    }
    const body = (await request.json().catch(() => ({}))) as {
      name?: string;
    };
    if (!body || !body.name) {
      return HttpResponse.json(
        { error: 'Invalid name provided' },
        { status: 400 }
      );
    }
    const name = body?.name;
    const newFolder = name;

    // Add to mock database
    const userFolder = mockFolders.get(userId) || [];
    userFolder.push(newFolder);
    mockFolders.set(userId, userFolder);

    // Initialize transcription progress tracking
    mockTranscriptionStartTime = Date.now();
    mockLastProgress = 0;

    // Mock successful response returning UploadedFile type
    return HttpResponse.json({ folder: newFolder });
  }),

  // Delete a note
  http.delete('/api/notes/:id', ({ params, request }) => {
    const authHeader = request.headers.get('Authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return HttpResponse.json(
        { error: 'Authorization required' },
        { status: 401 }
      );
    }

    const userId = validateToken(token);
    if (!userId) {
      return HttpResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    const noteId = Number(params.id);
    const userNotes = mockNotes.get(userId) || [];
    const noteIndex = userNotes.findIndex((note) => note.id === noteId);

    if (noteIndex === -1) {
      return HttpResponse.json({ error: 'Note not found' }, { status: 404 });
    }

    // Remove the note
    userNotes.splice(noteIndex, 1);
    mockNotes.set(userId, userNotes);

    return HttpResponse.json({
      success: true,
      message: 'Note deleted successfully'
    });
  }),

  // Download note in specific format
  http.get('/api/notes/:id/download/:format', ({ params, request }) => {
    const authHeader = request.headers.get('Authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return HttpResponse.json(
        { error: 'Authorization required' },
        { status: 401 }
      );
    }

    const userId = validateToken(token);
    if (!userId) {
      return HttpResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    const noteId = Number(params.id);
    const format = params.format as string;
    const userNotes = mockNotes.get(userId) || [];
    const note = userNotes.find((n) => n.id === noteId);

    if (!note) {
      return HttpResponse.json({ error: 'Note not found' }, { status: 404 });
    }

    if (note.status !== 'completed') {
      return HttpResponse.json(
        { error: 'Note not ready for download' },
        { status: 400 }
      );
    }

    // Redirect to the appropriate file based on format
    if (format === 'pdf') {
      return HttpResponse.redirect('/144.pdf');
    } else if (format === 'docx') {
      return HttpResponse.redirect('/144.docx');
    } else {
      return HttpResponse.json(
        { error: 'Unsupported format' },
        { status: 400 }
      );
    }
  })
];
