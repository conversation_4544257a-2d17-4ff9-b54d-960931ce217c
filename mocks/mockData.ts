import { UserImpl } from '~/types/User';
import type {
  UserProfile,
  UserPreferences,
  UserSubscription,
  UserUsage,
  SubscriptionPlan
} from '~/types/User';
import { Note } from '~/types/Note';

/**
 * Subscription plans
 */
export const SUBSCRIPTION_PLANS: SubscriptionPlan[] = [
  {
    id: 'free',
    name: 'Free',
    description: 'Perfect for getting started',
    price: 0,
    currency: 'EUR',
    billingInterval: 'monthly',
    minutes: 60,
    features: [
      '60 minutes per month',
      'Basic support',
      'Standard quality',
      'Basic features'
    ],
    limits: {
      maxFolders: 5,
      maxFileSize: 50 * 1024 * 1024, // 50MB
      supportLevel: 'basic'
    }
  },
  {
    id: 'pro',
    name: 'Pro',
    description: 'For professionals and small teams',
    price: 19.99,
    originalPrice: 29.99,
    currency: 'EUR',
    billingInterval: 'monthly',
    minutes: 300,
    features: [
      '300 minutes per month',
      'Priority support',
      'High quality',
      'Advanced features',
      'Custom folders'
    ],
    limits: {
      maxFolders: 25,
      maxFileSize: 200 * 1024 * 1024, // 200MB
      supportLevel: 'premium'
    },
    isPopular: true
  },
  {
    id: 'premium',
    name: 'Premium',
    description: 'For power users and large teams',
    price: 39.99,
    originalPrice: 49.99,
    currency: 'EUR',
    billingInterval: 'monthly',
    minutes: 1000,
    features: [
      '1000 minutes per month',
      '24/7 support',
      'Premium quality',
      'All features',
      'Unlimited folders',
      'API access'
    ],
    limits: {
      maxFileSize: 500 * 1024 * 1024, // 500MB
      supportLevel: 'premium'
    },
    isRecommended: true
  }
];

/**
 * Create mock user profile
 */
export function createUserProfile(
  overrides: Partial<UserProfile> = {}
): UserProfile {
  return {
    firstName: 'John',
    lastName: 'Doe',
    businessName: 'Acme Corp',
    phoneNumber: '+1234567890',
    avatar: '/avatars/default.jpg',
    timezone: 'Europe/Berlin',
    language: 'en',
    ...overrides
  };
}

/**
 * Create mock user preferences
 */
export function createUserPreferences(
  overrides: Partial<UserPreferences> = {}
): UserPreferences {
  return {
    notifications: {
      email: true,
      push: true,
      transcriptionComplete: true,
      weeklyDigest: true
    },
    defaultFolder: undefined,
    autoSave: true,
    ...overrides
  };
}

/**
 * Create mock user subscription
 */
export function createUserSubscription(
  planId: string = 'free',
  overrides: Partial<UserSubscription> = {}
): UserSubscription {
  const plan =
    SUBSCRIPTION_PLANS.find((p) => p.id === planId) || SUBSCRIPTION_PLANS[0];
  const now = new Date();
  const periodEnd = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000); // 30 days from now

  return {
    planId: plan.id,
    planName: plan.name,
    status: 'active',
    currentPeriodStart: now,
    currentPeriodEnd: periodEnd,
    totalMinutes: plan.minutes,
    minutesUsed: Math.floor(plan.minutes * 0.2), // 20% used by default
    minutesRemaining: Math.floor(plan.minutes * 0.8),
    autoRenew: true,
    ...overrides
  };
}

/**
 * Create mock user usage
 */
export function createUserUsage(overrides: Partial<UserUsage> = {}): UserUsage {
  return {
    totalNotes: 5,
    totalMinutesTranscribed: 50,
    averageNoteLength: 10,
    mostUsedFolder: 'General',
    lastActivity: new Date(),
    ...overrides
  };
}

/**
 * Create complete mock user
 */
export function createUser(
  id: number,
  email: string,
  profileOverrides: Partial<UserProfile> = {},
  subscriptionPlanId: string = 'free',
  preferencesOverrides: Partial<UserPreferences> = {},
  usageOverrides: Partial<UserUsage> = {}
): UserImpl {
  const profile = createUserProfile(profileOverrides);
  const subscription = createUserSubscription(subscriptionPlanId);
  const preferences = createUserPreferences(preferencesOverrides);
  const usage = createUserUsage(usageOverrides);

  return new UserImpl(
    id,
    email,
    true, // emailVerified
    new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // created 30 days ago
    new Date(), // updated now
    profile,
    subscription,
    preferences,
    usage
  );
}

/**
 * Create mock notes for a user
 */
export function createNotesForUser(userId: number, count: number = 5): Note[] {
  const notes: Note[] = [];
  const folders = ['General', 'Meetings', 'Interviews', 'Lectures', 'Personal'];
  const statuses = ['completed', 'processing', 'failed'] as const;

  for (let i = 0; i < count; i++) {
    const noteId = Date.now() + i;
    const randomFolder =
      Math.random() > 0.3
        ? folders[Math.floor(Math.random() * folders.length)]
        : null;
    const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
    const createdDate = new Date(
      Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000
    ); // Random date within last 30 days

    notes.push(
      new Note(
        noteId,
        `Note ${i + 1} - ${randomFolder || 'Untitled'}`,
        createdDate,
        `${Math.floor(Math.random() * 60)}:${Math.floor(Math.random() * 60)
          .toString()
          .padStart(2, '0')}`,
        randomFolder,
        randomStatus,
        userId,
        {
          fileId: `file-${noteId}`,
          transcriptionId: `trans-${noteId}`,
          fileUrl: `/mock-audio-${noteId}.mp3`,
          fileSize: Math.floor(Math.random() * 10000000) + 1000000, // 1-10MB
          fileType: 'audio/mpeg',
          createdAt: createdDate,
          updatedAt: createdDate
        }
      )
    );
  }

  return notes.sort((a, b) => b.date.getTime() - a.date.getTime());
}

/**
 * Create mock folders for a user
 */
export function createFoldersForUser(userId: number): string[] {
  const baseFolders = ['General', 'Meetings', 'Interviews'];

  // Add some user-specific folders based on their subscription
  if (userId === 1) {
    return ['General', 'Meetings', 'Interviews', 'Lectures', 'Personal'];
  } else if (userId === 2) {
    return ['General', 'Meetings', 'Interviews', 'Lectures', 'Personal'];
  }

  return baseFolders;
}

/**
 * Predefined mock users
 */
export const MOCK_USERS = [
  // Alice - Premium user
  createUser(
    1,
    '<EMAIL>',
    {
      firstName: 'Alice',
      lastName: 'Wonderland',
      businessName: 'Wonderland Consulting',
      phoneNumber: '+1234567890',
      avatar: '/avatars/alice.jpg'
    },
    'premium',
    {
      defaultFolder: 'Gemeindesitzungen'
    },
    {
      totalNotes: 15,
      totalMinutesTranscribed: 200,
      averageNoteLength: 13.3,
      mostUsedFolder: 'Gemeindesitzungen'
    }
  ),

  // Bob - Free user
  createUser(
    2,
    '<EMAIL>',
    {
      firstName: 'Bob',
      lastName: 'Dylan',
      businessName: 'Dylan Music Studio',
      phoneNumber: '+0987654321',
      timezone: 'America/New_York'
    },
    'free',
    {
      notifications: {
        email: false,
        push: true,
        transcriptionComplete: true,
        weeklyDigest: false
      },
      autoSave: false
    },
    {
      totalNotes: 3,
      totalMinutesTranscribed: 25,
      averageNoteLength: 8.3,
      mostUsedFolder: 'Client Calls'
    }
  )
];

/**
 * Get mock notes for all users
 */
export const MOCK_NOTES = new Map<number, Note[]>([
  [1, createNotesForUser(1, 12)],
  [2, createNotesForUser(2, 3)]
]);

/**
 * Get mock folders for all users
 */
export const MOCK_FOLDERS = new Map<number, string[]>([
  [1, createFoldersForUser(1)],
  [2, createFoldersForUser(2)]
]);
