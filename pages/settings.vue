<template>
  <div
    class="flex flex-col lg:flex-row! h-full px-3 gap-0! md:gap-0! overflow-y-auto"
  >
    <NavigationContainerTabs :items="items" />
    <div class="flex-1">
      <UIContainer justify="start!">
        <NuxtPage />
      </UIContainer>
    </div>
  </div>
</template>

<script setup lang="ts">
  const { t } = useI18n();
  definePageMeta({
    layout: 'default',
    middleware: ['auth', 'settings-redirect']
  });

  const localePath = useLocalePath();
  const route = useRoute();

  const activeIndex = ref(0);
  const items = ref([
    {
      label: t('settings.profile.title'),
      route: localePath('/settings/profile'),
      icon: 'mdi:account-outline'
    },
    {
      label: t('settings.login.title'),
      route: localePath('/settings/login-information'),
      icon: 'mdi:login-variant'
    },
    {
      label: t('settings.subscription.title'),
      route: localePath('/settings/subscription'),
      icon: 'mdi:credit-card-outline'
    },
    {
      label: t('settings.billing.title'),
      route: localePath('/settings/billing'),
      icon: 'mdi:file-outline'
    }
  ]);

  // Set active tab based on current route
  const setActiveTabFromRoute = () => {
    const currentPath = route.path;
    const index = items.value.findIndex((item) => item.route === currentPath);
    if (index !== -1) {
      activeIndex.value = index;
    }
  };

  onMounted(() => {
    setActiveTabFromRoute();
  });

  watch(
    () => route.path,
    () => {
      setActiveTabFromRoute();
    }
  );
</script>

<style lang="scss" scoped>
  @use '~/assets/styles/theme' as *;
  @use '~/assets/styles/colors' as *;
  @use '~/assets/styles/utils' as *;
</style>
