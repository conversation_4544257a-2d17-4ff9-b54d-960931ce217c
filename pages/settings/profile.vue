<template>
  <div class="card max-w-4xl!">
    <h1 class="card-title text-center!">
      {{ $t('settings.profile.title') }}
    </h1>
    <PrimeForm class="form" @submit="saveProfile">
      <div class="form-row">
        <div class="form-group">
          <label for="firstName">{{ $t('settings.profile.name.first') }}</label>
          <PrimeInputText
            id="firstName"
            v-model="profileForm.firstName"
            placeholder=""
          />
        </div>
        <div class="form-group">
          <label for="lastName">{{ $t('settings.profile.name.last') }}</label>
          <PrimeInputText
            id="lastName"
            v-model="profileForm.lastName"
            placeholder=""
          />
        </div>
      </div>

      <div class="form-group">
        <label for="businessName">{{
          $t('settings.profile.name.business')
        }}</label>
        <PrimeInputText
          id="businessName"
          v-model="profileForm.businessName"
          :placeholder="$t('settings.profile.name.business_placeholder')"
        />
      </div>

      <div class="form-group">
        <label for="phoneNumber">{{
          $t('settings.profile.telephone.title')
        }}</label>
        <PrimeInputText
          id="phoneNumber"
          v-model="profileForm.phoneNumber"
          :placeholder="$t('settings.profile.telephone.placeholder')"
        />
        <legend class="legend">
          {{ $t('settings.profile.telephone.paragraph') }}
        </legend>
      </div>

      <PrimeButton
        type="submit"
        label="Save settings"
        class="submit-button"
        :loading="userStore.isLoading"
      />
    </PrimeForm>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted } from 'vue';
  import { useUserStore } from '~/stores/user';
  import { useToast } from 'primevue/usetoast';
  import type { UserProfile } from '~/types/User';

  const userStore = useUserStore();
  const toast = useToast();

  const profileForm = ref<UserProfile>({
    firstName: '',
    lastName: '',
    businessName: '',
    phoneNumber: '',
    email: ''
  });

  onMounted(() => {
    // Initialize form with current user data
    if (userStore.user) {
      const profile = userStore.user.profile;
      if (profile) {
        profileForm.value = { ...profile };
      } else {
        // Extract from existing user data
        const nameParts = userStore.user.name.split(' ');
        profileForm.value.firstName = nameParts[0] || '';
        profileForm.value.lastName = nameParts.slice(1).join(' ') || '';
        profileForm.value.email = userStore.user.email || '';
      }
    }
  });

  const saveProfile = async () => {
    const result = await userStore.updateUserProfile(profileForm.value);

    if (result.success) {
      toast.add({
        severity: 'success',
        summary: 'Success',
        detail: result.message,
        life: 3000
      });
    } else {
      toast.add({
        severity: 'error',
        summary: 'Error',
        detail: result.message,
        life: 3000
      });
    }
  };
</script>

<style lang="scss" scoped>
  @use '~/assets/styles/theme' as *;
</style>
