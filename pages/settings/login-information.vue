<template>
  <div class="card max-w-4xl! m-0! p-8!">
    <h1 class="card-title">{{ $t('settings.login.email.title') }}</h1>
    <PrimeForm class="form" @submit="saveEmail">
      <div class="form-group">
        <label for="email">{{
          $t('settings.login.email.current', { email: userStore.user?.email })
        }}</label>
      </div>
      <div class="form-row">
        <FormInputText
          id="email-new"
          v-model="emailForm.email"
          label="settings.login.email.new"
          placeholder="settings.login.email.new_placeholder"
          type="email"
          required
        />
        <FormInputText
          id="email-confirm"
          v-model="emailForm.confirm"
          label="settings.login.email.confirm"
          placeholder="settings.login.email.confirm_placeholder"
          type="email"
          required
        />
      </div>
      <PrimeButton
        type="submit"
        :label="$t('settings.login.email.submit')"
        class="submit-button"
        :loading="userStore.isLoading"
      />
    </PrimeForm>
    <PrimeDivider class="py-12! light-divider" />
    <h1 class="card-title">{{ $t('settings.login.password.title') }}</h1>
    <PrimeForm class="form" @submit="savePassword">
      <FormPassword
        id="password-old"
        v-model="passwordForm.oldPass"
        :label="$t('settings.login.password.current')"
      />
      <div class="form-row">
        <FormPassword
          id="password-new"
          v-model="passwordForm.newPass"
          :label="$t('settings.login.password.new')"
          :meter="true"
        />
        <FormPassword
          id="password-confirm"
          v-model="passwordForm.confirmPass"
          :label="$t('settings.login.password.confirm')"
        />
      </div>
      <legend>{{ $t('settings.login.password.hint') }}</legend>
      <PrimeButton
        type="submit"
        :label="$t('settings.login.password.submit')"
        class="submit-button"
        :loading="userStore.isLoading"
      />
    </PrimeForm>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { useUserStore } from '~/stores/user';
  import { useToast } from 'primevue/usetoast';

  const userStore = useUserStore();
  const toast = useToast();

  const emailForm = ref({
    email: '',
    confirm: ''
  });

  const saveEmail = async () => {
    if (emailForm.value.email !== emailForm.value.confirm) {
      toast.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Emails do not match',
        life: 3000
      });
      return;
    }

    const result = await userStore.updateEmail(emailForm.value.email);

    if (!result.success) {
      toast.add({
        severity: 'error',
        summary: 'Error',
        detail: result.message,
        life: 3000
      });
    }
  };
  const passwordForm = ref({
    oldPass: '',
    newPass: '',
    confirmPass: ''
  });

  const savePassword = async () => {
    if (passwordForm.value.newPass !== passwordForm.value.confirmPass) {
      toast.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Passwords do not match',
        life: 3000
      });
      return;
    }

    const result = await userStore.updateEmail(emailForm.value.email);

    if (result.success) {
      toast.add({
        severity: 'success',
        summary: 'Success',
        detail: result.message,
        life: 3000
      });
    } else {
      toast.add({
        severity: 'error',
        summary: 'Error',
        detail: result.message,
        life: 3000
      });
    }
  };
</script>

<style lang="scss" scoped>
  @use '~/assets/styles/theme' as *;
</style>
