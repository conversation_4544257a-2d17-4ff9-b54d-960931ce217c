<template>
  <div class="card max-w-4xl! m-0!">
    <div class="plan-section">
      <h1 class="card-title">{{ $t('settings.subscription.title') }}</h1>
      <div class="plan-cards">
        <PrimeCard
          v-for="plan in userStore.availablePlans"
          :key="plan.id"
          class="plan-card"
          :class="{ 'current-plan': isCurrentPlan(plan.id) }"
        >
          <template #header>
            <div class="plan-header">
              <h3 class="plan-name">{{ plan.name }}</h3>
              <PrimeBadge
                v-if="plan.isPopular"
                value="Popular"
                severity="success"
                class="popular-badge"
              />
            </div>
          </template>

          <template #content>
            <div class="plan-content">
              <div class="plan-price">
                <span class="price">{{ `€${plan.price}` }}</span>
                <span class="period">/month</span>
              </div>

              <div class="plan-minutes">
                {{ plan.minutes }} minutes per month
              </div>

              <ul class="plan-features">
                <li v-for="feature in plan.features" :key="feature">
                  <Icon name="mdi:check" />
                  {{ feature }}
                </li>
              </ul>
            </div>
          </template>

          <template #footer>
            <PrimeButton
              v-if="isCurrentPlan(plan.id)"
              class="plan-button chosen-button"
              disabled
              severity="success"
            >
              <Icon name="mdi:check" /> Chosen
            </PrimeButton>
            <PrimeButton
              v-else
              class="plan-button"
              :loading="userStore.isLoading"
              @click="selectPlan(plan.id)"
              ><span class="text-black!">Choose Plan</span></PrimeButton
            >
          </template>
        </PrimeCard>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { useUserStore } from '~/stores/user';
  const userStore = useUserStore();

  const selectPlan = async (planId: string) => {
    await userStore.updateSubscriptionPlan(planId);
  };

  const isCurrentPlan = (planId: string) => {
    const currentPlan = userStore.getCurrentPlan();
    return currentPlan?.id === planId;
  };
</script>

<style lang="scss" scoped>
  @use '~/assets/styles/theme' as *;
  @use '~/assets/styles/colors' as *;

  .plan-section {
    max-width: 96rem;

    .plan-cards {
      display: grid;
      grid-template-columns: 1fr;
      gap: 1.5rem;
      width: 100%;
      max-width: 96rem;

      @media (min-width: 1024px) {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
      }

      @media (min-width: 1280px) {
        grid-template-columns: repeat(3, 1fr);
      }

      &.flex {
        display: grid;
      }
    }

    .plan-card {
      background: rgba(255, 255, 255, 0.02);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.16);
      border-radius: 1rem;
      transition: all 0.3s ease;
      width: 100%;
      min-height: 400px;
      display: flex;
      flex-direction: column;

      &:hover {
        transform: translateY(-4px);
        border-color: rgba(255, 255, 255, 0.3);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
      }

      &.current-plan {
        border-color: #10b981;
        box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
      }

      &.w-61 {
        width: 100%;
      }

      .plan-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.5rem 1.5rem 1rem;

        .plan-name {
          color: white;
          font-size: 1.5rem;
          font-weight: 600;
          margin: 0;
        }

        .popular-badge {
          background: #10b981;
          font-size: 0.75rem;
          padding: 0.25rem 0.75rem;
        }
      }

      .plan-content {
        padding: 0 1.5rem;
        color: white;
        flex-grow: 1;

        .plan-price {
          display: flex;
          align-items: baseline;
          gap: 0.5rem;
          margin-bottom: 1rem;

          .price {
            font-size: 2rem;
            font-weight: 700;
            color: white;
          }

          .period {
            color: rgba(255, 255, 255, 0.7);
            font-size: 1rem;
          }
        }

        .plan-minutes {
          font-size: 1.1rem;
          margin-bottom: 1.5rem;
          color: rgba(255, 255, 255, 0.9);
          font-weight: 500;
        }

        .plan-features {
          list-style: none;
          padding: 0;
          margin: 0;

          li {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 0.75rem;
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.95rem;

            .icon {
              color: #10b981;
              font-size: 1.1rem;
              flex-shrink: 0;
            }
          }
        }
      }

      .plan-button {
        margin: 1.5rem;
        margin-top: auto;
        width: calc(100% - 3rem);
        border-radius: 0.75rem;
        padding: 0.875rem 1rem;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;

        &:not(.chosen-button) {
          background: white;
          color: $black !important;
          border: none;

          &:hover:not(:disabled) {
            background: rgba(255, 255, 255, 0.9);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
          }
        }

        &.chosen-button {
          background: #10b981;
          border-color: #10b981;
          color: white;

          .icon {
            margin-right: 0.5rem;
          }
        }
      }
    }
  }
</style>
