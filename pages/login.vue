<template>
  <div class="container w-full">
    <div class="card max-w-lg!">
      <!-- Logo and Title -->
      <div class="login-header">
        <NuxtImg src="/logo.png" alt="logo" class="login-logo" />
        <h1 class="login-title"><span class="gradient">KI</span>-note</h1>
      </div>

      <div class="text-center">
        <h2 class="card-title">
          {{ $t('auth.login.title') }}
          <p class="login-description">
            {{ $t('auth.login.subtitle') }}
          </p>
        </h2>

        <PrimeForm class="form" @submit="handleLogin">
          <div class="form-group">
            <label class="text-left">{{ $t('auth.login.username') }}</label>
            <PrimeInputText v-model="credentials.username" type="text"
              :placeholder="$t('auth.login.username_placeholder')" required :disabled="isLoading" />
          </div>

          <div class="form-group">
            <label class="text-left">{{ $t('auth.login.password') }}</label>
            <PrimeInputText v-model="credentials.password" type="password"
              :placeholder="$t('auth.login.password_placeholder')" required :disabled="isLoading" />
          </div>

          <div class="form-group">
            <PrimeCheckbox v-model="credentials.rememberMe" input-id="rememberMe" binary />
            <label for="rememberMe" class="ml-2">{{ $t('auth.login.remember_me') }}</label>
          </div>

          <div v-if="errorMessage" class="error-message">
            {{ errorMessage }}
          </div>

          <PrimeButton type="submit" class="submit-button w-full! mt-2! mx-auto!" :loading="isLoading"
            :disabled="isLoading">
            {{
              isLoading
                ? $t('auth.login.loading')
                : $t('auth.login.submit')
            }}
          </PrimeButton>
        </PrimeForm>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue';

// Set layout
definePageMeta({
  layout: 'auth'
});

const { login, getRedirectPath, clearRedirectPath, isAuthenticated, isLoading } = useAuth();
const router = useRouter();
const { t } = useI18n();

const credentials = reactive({
  username: '',
  password: '',
  rememberMe: false
});

const errorMessage = ref('');

const handleLogin = async () => {
  errorMessage.value = '';

  const result = await login(credentials);

  if (result.success) {
    const redirectTo = getRedirectPath();
    clearRedirectPath();
    router.push(redirectTo);
  } else {
    errorMessage.value = result.message || t('auth.login.error');
  }
};

// Redirect if already logged in
onMounted(async () => {
  // Check if already authenticated
  if (isAuthenticated.value) {
    const redirectTo = getRedirectPath();
    clearRedirectPath();
    router.push(redirectTo);
  }
});
</script>

<style lang="scss" scoped>
@use '@/assets/styles/theme' as *;
@use '@/assets/styles/utils' as *;
@use '@/assets/styles/colors' as *;

.login-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 0.5rem;
  text-align: center;
  gap: 1rem;
  justify-content: center;
}

.login-logo {
  width: 6rem;
  height: 8.25rem;
}

.login-title {
  font-weight: 500;
  color: $text-white;
  text-align: center;

  .gradient {
    background: -webkit-linear-gradient(0deg,
        $text-gradient-start,
        $text-gradient-end );
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

.login-description {
  font-size: 1rem;
  color: $text-dark;
  margin-bottom: 2rem;
}
</style>
