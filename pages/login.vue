<template>
  <div class="container w-full">
    <div class="card max-w-lg!">
      <!-- Logo and Title -->
      <div class="login-header">
        <NuxtImg src="/logo.png" alt="logo" class="login-logo" />
        <h1 class="login-title"><span class="gradient">KI</span>-note</h1>
      </div>

      <div class="text-center">
        <h2 class="card-title">
          {{ $t('auth.login.title') }}
          <p class="login-description">
            {{ $t('auth.login.subtitle') }}
          </p>
        </h2>

        <PrimeForm class="form" @submit="handleLogin">
          <div class="form-group">
            <label class="text-left">{{ $t('auth.login.username') }}</label>
            <PrimeInputText
              v-model="credentials.username"
              type="text"
              :placeholder="$t('auth.login.username_placeholder')"
              required
              :disabled="userStore.isLoading"
            />
          </div>

          <div class="form-group">
            <label class="text-left">{{ $t('auth.login.password') }}</label>
            <PrimeInputText
              v-model="credentials.password"
              type="password"
              :placeholder="$t('auth.login.password_placeholder')"
              required
              :disabled="userStore.isLoading"
            />
          </div>

          <div v-if="errorMessage" class="error-message">
            {{ errorMessage }}
          </div>

          <PrimeButton
            type="submit"
            class="submit-button w-full! mt-2! mx-auto!"
            :loading="userStore.isLoading"
            :disabled="userStore.isLoading"
          >
            {{
              userStore.isLoading
                ? $t('auth.login.loading')
                : $t('auth.login.submit')
            }}
          </PrimeButton>
        </PrimeForm>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive } from 'vue';

  // Set layout
  definePageMeta({
    layout: 'auth'
  });

  const userStore = useUserStore();
  const router = useRouter();
  const { t } = useI18n();

  const credentials = reactive({
    username: '',
    password: ''
  });

  const errorMessage = ref('');

  const handleLogin = async () => {
    errorMessage.value = '';

    const result = await userStore.login(credentials);

    if (result.success) {
      const redirectTo = userStore.getRedirectPath();
      userStore.clearRedirectPath();
      router.push(redirectTo);
    } else {
      errorMessage.value = result.message || t('auth.login.error');
    }
  };

  // Redirect if already logged in
  onMounted(async () => {
    // Try to restore session first
    await userStore.initializeAuth();

    if (userStore.isLoggedIn) {
      const redirectTo = userStore.getRedirectPath();
      userStore.clearRedirectPath();
      router.push(redirectTo);
    }
  });
</script>

<style lang="scss" scoped>
  @use '@/assets/styles/theme' as *;
  @use '@/assets/styles/utils' as *;
  @use '@/assets/styles/colors' as *;

  .login-header {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 0.5rem;
    text-align: center;
    gap: 1rem;
    justify-content: center;
  }

  .login-logo {
    width: 6rem;
    height: 8.25rem;
  }

  .login-title {
    font-weight: 500;
    color: $text-white;
    text-align: center;

    .gradient {
      background: -webkit-linear-gradient(
        0deg,
        $text-gradient-start,
        $text-gradient-end
      );
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .login-description {
    font-size: 1rem;
    color: $text-dark;
    margin-bottom: 2rem;
  }
</style>
