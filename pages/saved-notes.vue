<template>
  <UIContainer>
    <template #header>
      <h1 class="card-title">
        {{ $t('titles.saved_notes') }}
      </h1>
    </template>
    <NotesFolderButtonRow v-model="selectedFolder" :folders="folders" />

    <UIGlassWrapper class="overflow-hidden! px-1! md:px-8! h-[82%]!">
      <template #header>
        <div class="flex flex-row! gap-4 mr-auto w-full! xl:w-[75%]!">
          <UISearch
            v-model="searchQuery"
            class="flex-2"
            :placeholder="$t('saved_notes.search')"
          />
          <UISelect
            v-model="selectedFilter"
            class="flex-1"
            :placeholder="$t('saved_notes.filter')"
            :options="filterOptions"
            show-clear
          />
          <UIButton
            variant="action"
            class="flex-1 justify-between!"
            @click="toggleBulkPopover"
          >
            {{ $t('saved_notes.select_options') }}
            <Icon name="mdi:chevron-down" />
          </UIButton>
          <UIPopOverList ref="bulkPopoverRef" append-to="body">
            <ul>
              <li
                v-for="action in actions"
                :key="action.value"
                class="popover-label"
                @click="handleAction(action.value)"
              >
                {{ action.label }}
              </li>
            </ul>
          </UIPopOverList>
        </div>
      </template>

      <NotesTable
        :notes="filteredNotes"
        :loading="loading"
        class="h-[50vh]!"
        @download="handleDownload"
        @selection="handleSelectionChange"
        @delete="handleDelete"
      />
    </UIGlassWrapper>
  </UIContainer>
</template>

<script lang="ts" setup>
  import { ref, computed } from 'vue';
  import { useToast } from 'primevue/usetoast';
  import { useI18n } from 'vue-i18n';
  import type { Note } from '@/types/Note';
  import type { UIPopOverListRef } from '@/components/UI/PopOverList.vue';

  definePageMeta({
    middleware: 'auth'
  });

  const toast = useToast();
  const { t } = useI18n();
  const userStore = useUserStore();
  const loading = computed(() => userStore.notesLoading);
  const notes = computed(() => userStore.userNotes);
  const folders = computed(() => userStore.userFolders);

  const searchQuery = ref('');
  const selectedFilter = ref(null);
  const filterOptions = ref([
    { label: t('saved_notes.filter_options.1w'), value: 7 },
    { label: t('saved_notes.filter_options.2w'), value: 14 },
    { label: t('saved_notes.filter_options.1m'), value: 31 }
  ]);
  const selectedNotes = ref<Array<Note>>([]);
  const selectedFolder = ref<string | null>(null);

  const filteredNotes = computed(() => {
    let result = [...notes.value];

    // Apply search filter
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase();
      result = result.filter(
        (note) =>
          note.name.toLowerCase().includes(query) ||
          (note.folder && note.folder.toLowerCase().includes(query))
      );
    }

    // Apply folder filter
    if (selectedFolder.value) {
      result = result.filter((note) => note.folder === selectedFolder.value);
    }

    // Apply time filter
    if (selectedFilter.value) {
      const now = new Date();
      let cutoff: Date | null;

      if (selectedFilter.value !== null) {
        cutoff = new Date(now.setDate(now.getDate() - selectedFilter.value));
      } else {
        cutoff = null;
      }

      if (cutoff) {
        result = result.filter((note) => {
          const createdAt = new Date(note.date);
          return createdAt >= cutoff;
        });
      }
    }

    return result;
  });

  const bulkPopoverRef = ref<UIPopOverListRef | null>(null);
  const toggleBulkPopover = (event: PointerEvent) => {
    bulkPopoverRef.value?.toggle(event);
  };
  const actions = computed(() => {
    const hasIncomplete = selectedNotes.value.some(
      (note) => note.status !== 'completed'
    );

    const baseActions = [
      { label: t('saved_notes.actions.delete'), value: 'delete' }
    ];

    if (!hasIncomplete) {
      baseActions.push(
        {
          label: t('saved_notes.actions.download_docx'),
          value: 'download_docx'
        },
        { label: t('saved_notes.actions.download_pdf'), value: 'download_pdf' }
      );
    }

    return baseActions;
  });

  const handleAction = async (value: string) => {
    const hasIncomplete = selectedNotes.value.some(
      (note) => note.status !== 'completed'
    );

    if (
      (value === 'download_docx' || value === 'download_pdf') &&
      hasIncomplete
    ) {
      toast.add({
        severity: 'error',
        summary: t('saved_notes.action_blocked'),
        detail: t('saved_notes.incomplete_notes_download_disabled'),
        life: 3000
      });
      return;
    }

    if (value === 'delete') {
      await handleDeleteMultiple();
    } else if (value === 'download_docx') {
      await handleDownloadMultiple('docx');
    } else if (value === 'download_pdf') {
      await handleDownloadMultiple('pdf');
    }
  };

  const handleDownload = async (note: Note, format: string) => {
    try {
      const success = await userStore.downloadNote(
        note.id,
        format as 'pdf' | 'docx'
      );

      if (!success) {
        throw new Error('Download failed');
      }
    } catch (err) {
      console.error('Download error:', err);
      toast.add({
        severity: 'error',
        summary: 'Download Failed',
        detail: 'Failed to download the note. Please try again.',
        life: 3000
      });
    }
  };

  const handleDelete = async (note: Note) => {
    try {
      const success = await userStore.deleteNote(note.id);

      if (!success) {
        throw new Error('Delete failed');
      }
    } catch (err) {
      console.error('Delete error:', err);
      toast.add({
        severity: 'error',
        summary: 'Delete Failed',
        detail: 'Failed to delete the note. Please try again.',
        life: 3000
      });
    }
  };

  const handleDeleteMultiple = async () => {
    for (const note of selectedNotes.value) {
      await handleDelete(note);
    }
  };

  const handleDownloadMultiple = async (format: 'pdf' | 'docx') => {
    for (const note of selectedNotes.value) {
      await handleDownload(note, format);
    }
  };

  const handleSelectionChange = (selectedIds: Array<number>) => {
    selectedNotes.value = filteredNotes.value.filter((note) =>
      selectedIds.includes(note.id)
    );
  };
</script>
