<template>
  <UIContainer>
    <template #header>
      <h1 class="card-title text-center! w-full!">
        {{ $t('support.title') }}
      </h1>
    </template>
    <UIGlassWrapper class="max-w-3xl! h-fit!">
      <PrimeForm v-if="!isSubmitting" class="form" @submit="submitForm">
        <div class="form-row">
          <FormInputText
            id="name"
            v-model="form.name"
            v-model:error="formError.name"
            label="support.form.name"
            placeholder="support.form.name_placeholder"
          />
          <FormInputText
            id="email"
            v-model="form.email"
            v-model:error="formError.email"
            label="support.form.email"
            placeholder="support.form.email_placeholder"
            type="email"
          />
        </div>

        <FormSelect
          id="category"
          v-model="form.category"
          v-model:error="formError.category"
          label="support.form.category.title"
          :options="categoryOptions"
          placeholder="support.form.category.placeholder"
        />
        <FormTextArea
          id="message"
          v-model="form.message"
          v-model:error="formError.message"
          label="support.form.message"
          placeholder="support.form.message_placeholder"
        />

        <UIButton variant="submit">
          {{ $t('support.form.send') }}
        </UIButton>
      </PrimeForm>
      <div v-else class="flex">
        <LoadingSpinner />
      </div>
    </UIGlassWrapper>
  </UIContainer>
</template>

<script setup lang="ts">
  import { useToast } from 'primevue/usetoast';
  import { ref, reactive } from 'vue';
  import { useI18n } from 'vue-i18n';

  definePageMeta({
    middleware: 'auth'
  });

  const { t } = useI18n();
  const toast = useToast();

  const form = reactive({
    name: '',
    email: '',
    category: '',
    message: ''
  });

  const formError = reactive<{
    name: string | null;
    email: string | null;
    category: string | null;
    message: string | null;
  }>({
    name: null,
    email: null,
    category: null,
    message: null
  });

  const isSubmitting = ref(false);

  const categoryOptions = [
    { label: t('support.form.category.technical'), value: 'technical' },
    { label: t('support.form.category.billing'), value: 'billing' },
    { label: t('support.form.category.feature'), value: 'feature' },
    { label: t('support.form.category.other'), value: 'other' }
  ];

  const submitForm = async () => {
    isSubmitting.value = true;

    formError.name = form.name ? null : t('form.required');
    formError.email = form.email ? null : t('form.required');
    formError.category = form.category ? null : t('form.required');
    formError.message = form.message ? null : t('form.required');

    if (
      formError.name ||
      formError.email ||
      formError.category ||
      formError.message
    ) {
      isSubmitting.value = false;
      return;
    }

    try {
      const result = await $fetch<string>('/api/support', {
        method: 'POST',
        body: {
          name: form.name,
          email: form.email,
          category: form.category,
          message: form.message
        }
      });

      Object.assign(form, {
        name: '',
        email: '',
        category: '',
        message: ''
      });

      toast.add({
        severity: 'success',
        summary: 'Success',
        detail: result,
        life: 3000
      });
    } catch {
      toast.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Error submitting form. Please try again.',
        life: 3000
      });
    } finally {
      isSubmitting.value = false;
    }
  };
</script>

<style lang="scss" scoped>
  @use '@/assets/styles/theme' as *;
  @use '@/assets/styles/utils' as *;
  @use '@/assets/styles/colors' as *;
</style>
