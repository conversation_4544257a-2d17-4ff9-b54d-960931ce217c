/* eslint-disable @typescript-eslint/no-explicit-any */
export default defineNuxtPlugin(() => {
  if (typeof window === 'undefined') return;

  const handlerKey = Object.keys(window).find((k) => {
    const val = (window as any)[k];
    return k.startsWith('__pv') && val?.utils?.DomHandler;
  });

  const domHandler = handlerKey
    ? (window as any)[handlerKey]?.utils?.DomHandler
    : null;

  if (!domHandler?.getScrollableParents) return;

  const original = domHandler.getScrollableParents.bind(domHandler);

  domHandler.getScrollableParents = (el: HTMLElement) => {
    const scrollables = original(el);

    // Remove our scroll area from the list so it doesn't close dropdown
    return scrollables.filter(
      (node: HTMLElement) => !node.classList.contains('form-scroll-area')
    );
  };
});
