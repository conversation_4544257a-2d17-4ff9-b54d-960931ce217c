import { useAuthStore } from '~/stores/auth';

export default defineNuxtPlugin(async () => {
  const authStore = useAuthStore();

  // Only initialize auth on client side
  if (import.meta.client) {
    // Initialize auth and restore session
    await authStore.initializeAuth();

    // Set up auth synchronization across tabs/windows
    const { setupSyncListeners } = useAuthSync();
    const cleanup = setupSyncListeners();

    // Store cleanup function for potential future use
    if (cleanup) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (window as any).__authSyncCleanup = cleanup;
    }

    // Set up periodic token verification (every 5 minutes)
    const tokenVerificationInterval = setInterval(
      async () => {
        if (authStore.isAuthenticated && authStore.token) {
          // Check if token is close to expiry and refresh if needed
          if (authStore.sessionExpiry) {
            const timeUntilExpiry =
              authStore.sessionExpiry.getTime() - Date.now();
            const fiveMinutes = 5 * 60 * 1000;

            if (timeUntilExpiry < fiveMinutes && timeUntilExpiry > 0) {
              await authStore.refreshAuthToken();
            }
          }

          // Verify token is still valid
          await authStore.verifyToken();
        }
      },
      5 * 60 * 1000
    ); // Every 5 minutes

    // Clean up interval on page unload
    window.addEventListener('beforeunload', () => {
      clearInterval(tokenVerificationInterval);
    });

    // Store interval reference for cleanup
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (window as any).__tokenVerificationInterval = tokenVerificationInterval;
  }
});
