export default defineNuxtRouteMiddleware(async (to) => {
  const userStore = useUserStore();

  // Always try to restore session first on protected routes
  if (!userStore.isAuthenticated) {
    await userStore.initializeAuth();
  }

  // If user is not authenticated and trying to access protected routes
  if (!userStore.isLoggedIn && to.path !== '/login') {
    userStore.setRedirectPath(to.fullPath);
    return navigateTo('/login');
  }

  // If user is authenticated and trying to access login page
  if (userStore.isLoggedIn && to.path === '/login') {
    return navigateTo('/dashboard');
  }
});
