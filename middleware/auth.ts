export default defineNuxtRouteMiddleware(async (to) => {
  const { isAuthenticated, initializeAuth, setRedirectPath } = useAuth();

  // Always try to restore session first on protected routes
  if (!isAuthenticated.value) {
    await initializeAuth();
  }

  // If user is not authenticated and trying to access protected routes
  if (!isAuthenticated.value && to.path !== '/login') {
    setRedirectPath(to.fullPath);
    return navigateTo('/login');
  }

  // If user is authenticated and trying to access login page
  if (isAuthenticated.value && to.path === '/login') {
    return navigateTo('/dashboard');
  }
});
