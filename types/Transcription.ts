export interface TranscriptionProgress {
  progress: number;
  status: 'processing' | 'completed';
  estimatedTime: number | undefined;
}

export class TranscriptionSettings {
  name: string;
  speakers: number | null;
  language: 'de' | 'en' | 'fr';
  structure: string;
  design: string;
  folder: string | null;
  transcribe: boolean;
  transcriptType: string;

  constructor(init?: Partial<TranscriptionSettings>) {
    this.name = '';
    this.speakers = null;
    this.language = 'de';
    this.structure = 'standard';
    this.design = 'standard';
    this.folder = null;
    this.transcribe = false;
    this.transcriptType = 'verbatim';

    Object.assign(this, init);
  }
}

export interface UploadedFile {
  file: File;
  name: string;
  size: number;
  type: string;
  url: string;
  duration?: string;
  id?: string;
  transcription?: {
    id: string;
    status: string;
    estimatedCompletionTime?: number;
    message?: string;
  };
}
