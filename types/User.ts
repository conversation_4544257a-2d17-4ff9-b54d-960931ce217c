// Base user identity
export interface UserIdentity {
  id: number;
  email: string;
  emailVerified: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// User profile information
export interface UserProfile {
  firstName: string;
  lastName: string;
  businessName?: string;
  phoneNumber?: string;
  avatar?: string;
  timezone?: string;
  language?: string;
}

// User preferences and settings
export interface UserPreferences {
  notifications: {
    email: boolean;
    push: boolean;
    transcriptionComplete: boolean;
    weeklyDigest: boolean;
  };
  defaultFolder?: string;
  autoSave: boolean;
}

// Subscription and billing information
export interface UserSubscription {
  planId: string;
  planName: string;
  status: 'active' | 'inactive' | 'cancelled' | 'past_due';
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  totalMinutes: number;
  minutesUsed: number;
  minutesRemaining: number;
  autoRenew: boolean;
}

// Usage statistics
export interface UserUsage {
  totalNotes: number;
  totalMinutesTranscribed: number;
  averageNoteLength: number;
  mostUsedFolder?: string;
  lastActivity: Date;
}

// Complete user data structure
export interface User extends UserIdentity {
  profile: UserProfile;
  subscription: UserSubscription;
  preferences: UserPreferences;
  usage: UserUsage;

  // Computed properties
  get fullName(): string;
  get initials(): string;
  get isSubscriptionActive(): boolean;
}

// Subscription plan definition
export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  currency: string;
  billingInterval: 'monthly' | 'yearly';
  minutes: number;
  features: string[];
  limits: {
    maxFolders?: number;
    maxFileSize?: number; // in bytes
    supportLevel: 'basic' | 'premium';
  };
  isPopular?: boolean;
  isRecommended?: boolean;
}

// API response types
export interface UserResponse {
  user: User;
}

export interface LoginResponse {
  success: boolean;
  user?: User;
  token?: string;
  refreshToken?: string;
  expiresIn?: number;
  message?: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

// User creation/update DTOs
export interface CreateUserRequest {
  email: string;
  password: string;
  profile: Omit<UserProfile, 'avatar'>;
  preferences?: Partial<UserPreferences>;
}

export interface UpdateUserProfileRequest {
  profile: Partial<UserProfile>;
}

export interface UpdateUserPreferencesRequest {
  preferences: Partial<UserPreferences>;
}

// User class implementation with computed properties
export class UserImpl implements User {
  constructor(
    public id: number,
    public email: string,
    public emailVerified: boolean,
    public createdAt: Date,
    public updatedAt: Date,
    public profile: UserProfile,
    public subscription: UserSubscription,
    public preferences: UserPreferences,
    public usage: UserUsage
  ) {
    this.id = id;
    this.email = email;
    this.emailVerified = emailVerified;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
    this.profile = profile;
    this.subscription = subscription;
    this.preferences = preferences;
    this.usage = usage;
  }

  get fullName(): string {
    return `${this.profile.firstName} ${this.profile.lastName}`.trim();
  }

  get initials(): string {
    const first = this.profile.firstName.charAt(0).toUpperCase();
    const last = this.profile.lastName.charAt(0).toUpperCase();
    return `${first}${last}`;
  }

  get isSubscriptionActive(): boolean {
    return (
      this.subscription.status === 'active' &&
      new Date() < this.subscription.currentPeriodEnd
    );
  }

  // Factory method for creating from API response
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  static fromJSON(data: any): UserImpl {
    return new UserImpl(
      data.id,
      data.email,
      data.emailVerified,
      new Date(data.createdAt),
      new Date(data.updatedAt),
      data.profile,
      {
        ...data.subscription,
        currentPeriodStart: new Date(data.subscription.currentPeriodStart),
        currentPeriodEnd: new Date(data.subscription.currentPeriodEnd)
      },
      data.preferences,
      {
        ...data.usage,
        lastActivity: new Date(data.usage.lastActivity)
      }
    );
  }

  // Convert to JSON for API requests
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  toJSON(): any {
    return {
      id: this.id,
      email: this.email,
      emailVerified: this.emailVerified,
      createdAt: this.createdAt.toISOString(),
      updatedAt: this.updatedAt.toISOString(),
      profile: this.profile,
      subscription: {
        ...this.subscription,
        currentPeriodStart: this.subscription.currentPeriodStart.toISOString(),
        currentPeriodEnd: this.subscription.currentPeriodEnd.toISOString()
      },
      preferences: this.preferences,
      usage: {
        ...this.usage,
        lastActivity: this.usage.lastActivity.toISOString()
      }
    };
  }
}
