export type NoteStatus = 'processing' | 'completed' | 'failed' | 'pending';

export interface NoteData {
  id: number;
  name: string;
  date: Date;
  duration: string;
  folder: string | null;
  status: NoteStatus;
  userId: number;
  fileId?: string;
  transcriptionId?: string;
  fileUrl?: string;
  fileSize?: number;
  fileType?: string;
  createdAt: Date;
  updatedAt: Date;
}

export class Note implements NoteData {
  id: number;
  name: string;
  date: Date;
  duration: string;
  folder: string | null;
  status: NoteStatus;
  userId: number;
  fileId?: string;
  transcriptionId?: string;
  fileUrl?: string;
  fileSize?: number;
  fileType?: string;
  createdAt: Date;
  updatedAt: Date;

  constructor(
    id: number,
    name: string,
    date: Date,
    duration: string,
    folder: string | null,
    status: NoteStatus = 'pending',
    userId: number,
    options?: {
      fileId?: string;
      transcriptionId?: string;
      fileUrl?: string;
      fileSize?: number;
      fileType?: string;
      createdAt?: Date;
      updatedAt?: Date;
    }
  ) {
    this.id = id;
    this.name = name;
    this.date = date;
    this.duration = duration;
    this.folder = folder;
    this.status = status;
    this.userId = userId;
    this.fileId = options?.fileId;
    this.transcriptionId = options?.transcriptionId;
    this.fileUrl = options?.fileUrl;
    this.fileSize = options?.fileSize;
    this.fileType = options?.fileType;
    this.createdAt = options?.createdAt || new Date();
    this.updatedAt = options?.updatedAt || new Date();
  }

  updateStatus(status: NoteStatus) {
    this.status = status;
    this.updatedAt = new Date();
  }

  toJSON(): NoteData {
    return {
      id: this.id,
      name: this.name,
      date: this.date,
      duration: this.duration,
      folder: this.folder,
      status: this.status,
      userId: this.userId,
      fileId: this.fileId,
      transcriptionId: this.transcriptionId,
      fileUrl: this.fileUrl,
      fileSize: this.fileSize,
      fileType: this.fileType,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }

  static fromJSON(data: NoteData): Note {
    return new Note(
      data.id,
      data.name,
      new Date(data.date),
      data.duration,
      data.folder,
      data.status,
      data.userId,
      {
        fileId: data.fileId,
        transcriptionId: data.transcriptionId,
        fileUrl: data.fileUrl,
        fileSize: data.fileSize,
        fileType: data.fileType,
        createdAt: new Date(data.createdAt),
        updatedAt: new Date(data.updatedAt)
      }
    );
  }
}
