export const useAuthSync = () => {
  const authStore = useAuthStore();

  // Handle page visibility changes
  const handleVisibilityChange = () => {
    if (!document.hidden && import.meta.client) {
      // Page became visible, sync auth state
      authStore.syncAuthState();
    }
  };

  // Handle focus events
  const handleFocus = () => {
    if (import.meta.client) {
      // Window gained focus, sync auth state
      authStore.syncAuthState();
    }
  };

  // Set up event listeners
  const setupSyncListeners = () => {
    if (!import.meta.client) return;

    // Listen for page visibility changes
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Listen for window focus events
    window.addEventListener('focus', handleFocus);

    // Cleanup function
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
    };
  };

  return {
    setupSyncListeners
  };
};
