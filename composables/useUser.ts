import { computed, type ComputedRef } from 'vue';
import { useAuthStore } from '~/stores/auth';
import { useUserProfileStore } from '~/stores/userProfile';
import type { User, UserProfile, UserPreferences, UserSubscription } from '~/types/User';

/**
 * Main user composable that provides reactive access to user data and actions
 */
export const useUser = () => {
  const authStore = useAuthStore();
  const profileStore = useUserProfileStore();

  // Reactive user data
  const user: ComputedRef<User | null> = computed(() => authStore.user);
  const isAuthenticated: ComputedRef<boolean> = computed(() => authStore.isAuthenticated);
  const isLoading: ComputedRef<boolean> = computed(() => authStore.isLoading || profileStore.isLoading);

  // User profile data
  const profile: ComputedRef<UserProfile | null> = computed(() => profileStore.currentProfile);
  const preferences: ComputedRef<UserPreferences | null> = computed(() => profileStore.currentPreferences);
  const subscription: ComputedRef<UserSubscription | null> = computed(() => profileStore.currentSubscription);

  // Computed user properties
  const fullName: ComputedRef<string> = computed(() => authStore.userFullName);
  const initials: ComputedRef<string> = computed(() => authStore.userInitials);
  const email: ComputedRef<string> = computed(() => authStore.userEmail);
  const isSubscriptionActive: ComputedRef<boolean> = computed(() => authStore.isSubscriptionActive);

  // Subscription helpers
  const currentPlan = computed(() => profileStore.getCurrentPlan());
  const remainingMinutes = computed(() => profileStore.getRemainingMinutes());
  const usagePercentage = computed(() => profileStore.getUsagePercentage());
  const canUpgrade = computed(() => profileStore.canUpgrade());

  return {
    // State
    user: readonly(user),
    isAuthenticated: readonly(isAuthenticated),
    isLoading: readonly(isLoading),
    
    // Profile data
    profile: readonly(profile),
    preferences: readonly(preferences),
    subscription: readonly(subscription),
    
    // Computed properties
    fullName: readonly(fullName),
    initials: readonly(initials),
    email: readonly(email),
    isSubscriptionActive: readonly(isSubscriptionActive),
    
    // Subscription helpers
    currentPlan: readonly(currentPlan),
    remainingMinutes: readonly(remainingMinutes),
    usagePercentage: readonly(usagePercentage),
    canUpgrade: readonly(canUpgrade),
    
    // Actions
    updateProfile: profileStore.updateProfile,
    updatePreferences: profileStore.updatePreferences,
    updateEmail: profileStore.updateEmail,
    updatePassword: profileStore.updatePassword,
    updateSubscription: profileStore.updateSubscription,
    deleteAccount: profileStore.deleteAccount,
    
    // Auth actions
    login: authStore.login,
    logout: authStore.logout,
    
    // Error handling
    error: computed(() => profileStore.error),
    clearError: profileStore.clearError
  };
};

/**
 * Authentication-specific composable
 */
export const useAuth = () => {
  const authStore = useAuthStore();

  return {
    // State
    isAuthenticated: computed(() => authStore.isAuthenticated),
    isLoading: computed(() => authStore.isLoading),
    user: computed(() => authStore.user),
    token: computed(() => authStore.token),
    
    // Actions
    login: authStore.login,
    logout: authStore.logout,
    verifyToken: authStore.verifyToken,
    refreshToken: authStore.refreshAuthToken,
    initializeAuth: authStore.initializeAuth,
    
    // Session management
    setRedirectPath: authStore.setRedirectPath,
    getRedirectPath: authStore.getRedirectPath,
    clearRedirectPath: authStore.clearRedirectPath,
    
    // Sync
    syncAuthState: authStore.syncAuthState
  };
};

/**
 * User profile management composable
 */
export const useUserProfile = () => {
  const profileStore = useUserProfileStore();

  return {
    // State
    isLoading: computed(() => profileStore.isLoading),
    error: computed(() => profileStore.error),
    
    // Data
    profile: computed(() => profileStore.currentProfile),
    preferences: computed(() => profileStore.currentPreferences),
    subscription: computed(() => profileStore.currentSubscription),
    
    // Plans
    availablePlans: computed(() => profileStore.getAvailablePlans()),
    currentPlan: computed(() => profileStore.getCurrentPlan()),
    
    // Usage
    remainingMinutes: computed(() => profileStore.getRemainingMinutes()),
    usagePercentage: computed(() => profileStore.getUsagePercentage()),
    canUpgrade: computed(() => profileStore.canUpgrade()),
    
    // Actions
    updateProfile: profileStore.updateProfile,
    updatePreferences: profileStore.updatePreferences,
    updateEmail: profileStore.updateEmail,
    updatePassword: profileStore.updatePassword,
    updateSubscription: profileStore.updateSubscription,
    deleteAccount: profileStore.deleteAccount,
    
    // Error handling
    clearError: profileStore.clearError
  };
};

/**
 * Subscription-specific composable
 */
export const useSubscription = () => {
  const profileStore = useUserProfileStore();
  const authStore = useAuthStore();

  const subscription = computed(() => authStore.user?.subscription);
  const currentPlan = computed(() => profileStore.getCurrentPlan());
  const availablePlans = computed(() => profileStore.getAvailablePlans());

  // Usage calculations
  const remainingMinutes = computed(() => {
    return subscription.value?.minutesRemaining || 0;
  });

  const usedMinutes = computed(() => {
    return subscription.value?.minutesUsed || 0;
  });

  const totalMinutes = computed(() => {
    return subscription.value?.totalMinutes || 0;
  });

  const usagePercentage = computed(() => {
    const total = totalMinutes.value;
    const used = usedMinutes.value;
    return total > 0 ? Math.round((used / total) * 100) : 0;
  });

  const isNearLimit = computed(() => {
    return usagePercentage.value >= 80;
  });

  const isOverLimit = computed(() => {
    return remainingMinutes.value <= 0;
  });

  const daysUntilRenewal = computed(() => {
    if (!subscription.value?.currentPeriodEnd) return 0;
    const now = new Date();
    const end = new Date(subscription.value.currentPeriodEnd);
    const diffTime = end.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  });

  const canUpgrade = computed(() => {
    const planId = subscription.value?.planId;
    return planId === 'free' || planId === 'pro';
  });

  const canDowngrade = computed(() => {
    const planId = subscription.value?.planId;
    return planId === 'pro' || planId === 'premium';
  });

  return {
    // State
    subscription: readonly(subscription),
    currentPlan: readonly(currentPlan),
    availablePlans: readonly(availablePlans),
    
    // Usage
    remainingMinutes: readonly(remainingMinutes),
    usedMinutes: readonly(usedMinutes),
    totalMinutes: readonly(totalMinutes),
    usagePercentage: readonly(usagePercentage),
    
    // Status
    isNearLimit: readonly(isNearLimit),
    isOverLimit: readonly(isOverLimit),
    daysUntilRenewal: readonly(daysUntilRenewal),
    canUpgrade: readonly(canUpgrade),
    canDowngrade: readonly(canDowngrade),
    
    // Actions
    updateSubscription: profileStore.updateSubscription,
    
    // Helpers
    formatMinutes: (minutes: number): string => {
      const hours = Math.floor(minutes / 60);
      const mins = minutes % 60;
      if (hours > 0) {
        return `${hours}h ${mins}m`;
      }
      return `${mins}m`;
    },
    
    getUsageColor: (): string => {
      const percentage = usagePercentage.value;
      if (percentage >= 90) return 'red';
      if (percentage >= 80) return 'orange';
      if (percentage >= 60) return 'yellow';
      return 'green';
    }
  };
};

/**
 * User preferences composable
 */
export const useUserPreferences = () => {
  const profileStore = useUserProfileStore();
  const authStore = useAuthStore();

  const preferences = computed(() => authStore.user?.preferences);

  // Theme helpers
  const isDarkMode = computed(() => {
    const theme = preferences.value?.theme;
    if (theme === 'system') {
      // Check system preference
      return import.meta.client && window.matchMedia('(prefers-color-scheme: dark)').matches;
    }
    return theme === 'dark';
  });

  const toggleTheme = async () => {
    const currentTheme = preferences.value?.theme || 'system';
    let newTheme: 'light' | 'dark' | 'system';
    
    switch (currentTheme) {
      case 'light':
        newTheme = 'dark';
        break;
      case 'dark':
        newTheme = 'system';
        break;
      default:
        newTheme = 'light';
    }
    
    await profileStore.updatePreferences({ theme: newTheme });
  };

  return {
    // State
    preferences: readonly(preferences),
    isDarkMode: readonly(isDarkMode),
    
    // Actions
    updatePreferences: profileStore.updatePreferences,
    toggleTheme,
    
    // Notification helpers
    isEmailNotificationsEnabled: computed(() => preferences.value?.notifications.email ?? true),
    isPushNotificationsEnabled: computed(() => preferences.value?.notifications.push ?? true),
    isTranscriptionNotificationsEnabled: computed(() => preferences.value?.notifications.transcriptionComplete ?? true),
    isWeeklyDigestEnabled: computed(() => preferences.value?.notifications.weeklyDigest ?? true)
  };
};
