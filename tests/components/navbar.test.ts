import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import Navbar from '~/components/Navigation/Main.vue';

describe('Navigation', () => {
  it('renders all navigation links', () => {
    const wrapper = mount(Navbar);

    const links = wrapper.findAll('a');
    expect(links).toHaveLength(6);
    expect(links[0].text()).toBe('Dashboard');
    expect(links[1].text()).toBe('Notes');
    expect(links[2].text()).toBe('Protocol');
    expect(links[3].text()).toBe('Settings');
    expect(links[4].text()).toBe('Support');
    expect(links[5].text()).toBe('Team');
  });

  it('has correct href attributes', () => {
    const wrapper = mount(Navbar);

    const hrefs = wrapper.findAll('a').map((a) => a.attributes('href'));
    expect(hrefs).toEqual([
      '/dashboard',
      '/notes',
      '/protocol',
      '/settings',
      '/support',
      '/team'
    ]);
  });
});
