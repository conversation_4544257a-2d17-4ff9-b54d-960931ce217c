![Build Status](https://gitlab.com/pages/nuxt/badges/master/build.svg)

---

Example [Nuxt](https://nuxt.com) website using GitLab Pages.

---

## Building locally

To work locally with this project, you'll have to follow the steps below:

1. Fork, clone or download this project
2. Install dependencies: `npm install`
3. Generate and preview the website with hot-reloading: `npm run dev` or `nuxt`
4. Add content

Read more at Nuxt's [documentation](https://nuxt.com/docs/getting-started/introduction).

## Folder Structure

---
