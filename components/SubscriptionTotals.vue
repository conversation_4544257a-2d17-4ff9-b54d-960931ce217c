<template>
  <div class="flex flex-3!">
    <div class="flex flex-col w-full max-w-4xl! py-4">
      <div class="text-right text-lg pb-1">
        {{ user.minutesLeft }} / {{ user.totalMinutes }}
        {{ $t('subscription.minutes') }}
      </div>
      <PrimeProgressBar :show-value="false" :value="value" />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import type { User } from '~/types/User';
  import { computed } from 'vue';

  const props = defineProps({
    user: {
      type: Object as () => User,
      required: true
    }
  });

  const value = computed(() => {
    if (!props.user.totalMinutes) return 0;
    return (
      ((props.user.totalMinutes - props.user.minutesLeft) /
        props.user.totalMinutes) *
      100
    );
  });
</script>

<style lang="scss">
  @use '@/assets/styles/theme' as *;
  @use '@/assets/styles/utils' as *;
  @use '@/assets/styles/colors' as *;

  .p-progressbar .p-progressbar-value {
    border-radius: 20rem 0 0 20rem;
  }
</style>
