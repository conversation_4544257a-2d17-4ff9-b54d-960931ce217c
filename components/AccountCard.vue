<template>
  <PrimeDivider class="light-divider" />
  <div v-if="user" class="account flex flex-row items-center justify-between p-2 gap-2 mb-4 md:mb-0">
    <div class="flex flex-row items-center gap-2 flex-1 min-w-0!">
      <PrimeAvatar :label="user.initials" size="xl" class="border p-2 font-extrabold" shape="circle" />
      <div class="flex flex-col min-w-0!">
        <div class="font-extrabold! overflow-hidden! text-ellipsis! whitespace-nowrap!">
          {{ user.name }}
        </div>
        <p>{{ $t('trial.minutes.left', { minutes: user.minutesLeft }) }}</p>
      </div>
    </div>
    <PrimeButton variant="logout" class="h-full w-12 rounded-sm! flex-shrink-0!" @click="logout">
      <Icon name="mdi:logout-variant" class="h-6! w-6!" />
    </PrimeButton>
  </div>
  <div v-else>
    <LoadingSpinner />
  </div>
</template>

<script lang="ts" setup>
import { useUser } from '~/composables/useUser';

const { user, logout } = useUser();
</script>

<style lang="scss">
@use '@/assets/styles/theme' as *;
@use '@/assets/styles/utils' as *;
@use '@/assets/styles/colors' as *;

.account {
  p {
    font-size: 0.875rem;
    font-weight: 400;
  }
}
</style>
