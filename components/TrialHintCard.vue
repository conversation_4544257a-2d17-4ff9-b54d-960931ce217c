<template>
  <div v-if="user && showElement" class="w-full!">
    <div class="backdrop">
      <div class="flex flex-row justify-between mb-2">
        <span> {{ $t('trial.card.title') }}</span>
        <PrimeButton variant="text" class="rounded-sm!" @click="hideElement">
          <Icon class="icon h-5" name="mdi:close" />
        </PrimeButton>
      </div>

      <p>
        {{ $t('trial.card.paragraph', { minutes: user.minutesLeft }) }}
      </p>
      <NuxtLink class="py-1!" :to="localePath('/settings/subscription')">{{
        $t('trial.card.link')
        }}</NuxtLink>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { useUser } from '~/composables/useUser';

const localePath = useLocalePath();
const { user } = useUser();
const showElement = ref(computed(() => user.value && user.value.subscriptionType === 'Free'));

function hideElement() {
  showElement.value = false;
}
</script>

<style lang="scss">
@use '@/assets/styles/theme' as *;
@use '@/assets/styles/utils' as *;
@use '@/assets/styles/colors' as *;

.backdrop {
  background: linear-gradient(135deg, #5d267a88, #37125088, #25697a88);
  border-radius: 1rem;
  padding: 1rem;

  span {
    font-size: 1rem;
    font-weight: 600;
    padding-left: 8px;
  }

  p {
    font-size: 0.875rem;
    margin-bottom: 0.75rem;
    padding-left: 8px;
  }

  a {
    font-size: 1rem;
    font-weight: 700;
    color: $black;
    background-color: $white;
    padding: 0.75rem;
    border-radius: 2rem;
    display: inline-block;
    text-align: center;

    &:hover {
      background-color: $light-gray;
      border-radius: 2rem;
      transform: translateY(-1px);
    }
  }
}
</style>
