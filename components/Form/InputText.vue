<template>
  <div class="form-group">
    <label
      :for="id"
      class="block truncate text-ellipsis overflow-hidden whitespace-nowrap text-white font-[500]"
    >
      {{ $t(label) }}
    </label>
    <PrimeInputText
      :id="id"
      v-model="modelValue"
      :placeholder="$t(placeholder ?? '')"
      :type="type"
      :class="{ 'text-[#FFFFFFA0]!': !modelValue, 'p-invalid': error }"
      @input="onInput"
    />
    <small v-if="error" class="error-hint mt-1">{{ error }}</small>
  </div>
</template>

<script lang="ts" setup>
  const modelValue = defineModel<string>();
  const emit = defineEmits(['update:modelValue', 'update:error']);

  defineProps<{
    id: string;
    label: string;
    error?: string | null;
    placeholder?: string;
    type?: string;
  }>();

  const onInput = (event: Event) => {
    const value = (event.target as HTMLInputElement).value;
    emit('update:modelValue', value);
    emit('update:error', null);
  };
</script>

<style lang="scss">
  @use '@/assets/styles/theme' as *;
  @use '@/assets/styles/colors' as *;

  textarea {
    @extend .form-input;
    height: 100% !important;
  }
</style>
