<template>
  <div class="form-group w-full!">
    <label
      :for="id"
      class="block truncate text-ellipsis overflow-hidden whitespace-nowrap text-white font-[500]"
    >
      {{ label }}
    </label>
    <PrimePassword
      v-model="modelValueProxy"
      required
      :input-id="id"
      :input-class="
        meter ? 'meter form-password flex-1!' : 'form-password flex-1!'
      "
      panel-class="password-overlay"
      :feedback="meter"
      :autocomplete="autocomplete"
      :pt="{}"
      :prompt-label="$t('settings.login.password.meter')"
      :weak-label="$t('settings.login.password.weak')"
      :medium-label="$t('settings.login.password.medium')"
      :strong-label="$t('settings.login.password.strong')"
      :class="{ 'p-invalid': error }"
    />
    <small v-if="error" class="error-hint mt-1">{{ error }}</small>
  </div>
</template>

<script lang="ts" setup>
  import { computed } from 'vue';

  const props = defineProps<{
    modelValue: string;
    error?: string | null;
    id?: string;
    label?: string;
    autocomplete?: string;
    meter?: boolean;
  }>();

  const emit = defineEmits(['update:modelValue', 'update:error']);

  const modelValueProxy = computed({
    get: () => props.modelValue,
    set: (val: string) => {
      emit('update:modelValue', val);
      emit('update:error', null); // Clear error on input
    }
  });

  const id = computed(() => props.id ?? 'password-input');
  const label = computed(() => props.label ?? 'Password');
</script>

<style scoped lang="scss">
  @use '~/assets/styles/theme' as *;
</style>
