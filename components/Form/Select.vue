<template>
  <div class="form-group">
    <label
      :for="id"
      class="block truncate text-ellipsis overflow-hidden whitespace-nowrap text-white font-[500]"
    >
      {{ $t(label) }}
    </label>
    <PrimeSelect
      :id="id"
      v-model="modelValue"
      :options="options"
      option-label="label"
      option-value="value"
      :placeholder="$t(placeholder ?? '')"
      class="form-select"
      overlay-class="dropdown-overlay"
      :pt="{
        overlay: {
          class: 'dropdown-overlay'
        },
        option: { class: 'options-label' }
      }"
      :append-to="appendTo"
      :class="['form-select', { 'p-invalid': error }]"
      @update:model-value="onUpdate"
    />
    <small v-if="error" class="error-hint mt-1">{{ error }}</small>
  </div>
</template>

<script lang="ts" setup>
  const modelValue = defineModel<string | number | null>();

  const emit = defineEmits(['update:modelValue', 'update:error']);

  defineProps<{
    id: string;
    label: string;
    options: Array<{ label: string; value: string | number }>;
    error?: string | null;
    placeholder?: string;
    appendTo?: string;
  }>();

  const onUpdate = (value: string | number | null) => {
    emit('update:modelValue', value);
    emit('update:error', null); // clear error on input
  };
</script>

<style lang="scss" scoped>
  @use '@/assets/styles/theme' as *;
  @use '@/assets/styles/colors' as *;
</style>
