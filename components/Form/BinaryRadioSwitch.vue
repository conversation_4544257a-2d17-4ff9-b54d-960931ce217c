<template>
  <div class="form-group gap-3!">
    <label
      :for="id"
      class="block truncate text-ellipsis overflow-hidden whitespace-nowrap text-white font-[500]"
    >
      {{ $t(label) }}
    </label>

    <div class="flex gap-5!">
      <div
        v-for="option in options"
        :key="option.label"
        class="flex items-center"
      >
        <PrimeRadioButton
          v-model="modelValue"
          :input-id="id + '-' + option.value"
          :value="option.value"
        />
        <label
          :for="id + '-' + option.value"
          class="radio ml-2 flex items-center gap-2 transition-all duration-300"
          :class="{ 'radio-selected': modelValue === option.value }"
        >
          <span>{{ option.label }}</span>
        </label>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  const modelValue = defineModel<boolean>({ required: true });

  defineProps<{
    id: string;
    label: string;
    options: Array<{ label?: string; icon?: string; value: boolean }>;
  }>();
</script>

<style scoped lang="scss">
  @use '~/assets/styles/colors' as *;
</style>
