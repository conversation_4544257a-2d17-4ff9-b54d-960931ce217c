<template>
  <div class="custom-grid">
    <div
      v-for="(feature, index) in visibleFeatures"
      :key="index"
      class="flex-1! flex!"
    >
      <NuxtLink
        :to="localePath(feature.link)"
        class="card-hover hidden sm:flex!"
      >
        <Icon :name="feature.icon" class="h-12!" />
        <h3>{{ $t(feature.title) }}</h3>
        <div>{{ $t(feature.description) }}</div>
      </NuxtLink>
    </div>
  </div>
</template>

<script lang="ts" setup>
  const localePath = useLocalePath();

  const features = [
    {
      key: 'protocol',
      link: '/chat',
      icon: 'mdi:auto-fix',
      title: 'dashboard.features.protocol.title',
      description: 'dashboard.features.protocol.description'
    },
    {
      key: 'template',
      link: '/template',
      icon: 'mdi:account-group-outline',
      title: 'dashboard.features.template.title',
      description: 'dashboard.features.template.description'
    },
    {
      key: 'design',
      link: '/design',
      icon: 'mdi:format-list-bulleted-square',
      title: 'dashboard.features.design.title',
      description: 'dashboard.features.design.description'
    }
  ];

  const screenColumns = ref(1);

  const updateColumns = () => {
    const width = window.innerWidth;
    if (width >= 1024)
      screenColumns.value = 3; // lg
    else if (width >= 768)
      screenColumns.value = 2; // md
    else if (width >= 640)
      screenColumns.value = 3; // sm
    else screenColumns.value = 0;
  };

  onMounted(() => {
    updateColumns();
    window.addEventListener('resize', updateColumns);
  });

  const visibleFeatures = computed(() =>
    features.slice(0, screenColumns.value)
  );
</script>

<style lang="scss" scoped>
  @use '@/assets/styles/theme' as *;
  @use '@/assets/styles/utils' as *;
  @use '@/assets/styles/colors' as *;

  .card-hover {
    @extend .card;
    flex-direction: column !important;
    align-items: flex-start !important;
    gap: 0.5rem !important;
    width: 100%;
    padding: 1rem;

    &:hover {
      background-color: $backdrop-5 !important;
      border-radius: 0.75rem !important;
      box-shadow: 0 0 2px 1px $backdrop-2 !important;
    }
  }

  .custom-grid {
    display: none;
    gap: 1rem;
    margin-bottom: 1rem;
    align-items: stretch;

    /* Show grid on small screens and up */
    @media (min-width: 40rem) {
      display: grid;
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }

    /* Override for medium screens */
    @media (min-width: 768px) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    /* Override again for large screens */
    @media (min-width: 1024px) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }

    /* Hide completely on short screen heights */
    @media (max-height: 42rem) {
      display: none !important;
    }
  }
</style>
