<template>
  <UIModal
    v-model:visible="dialogVisible"
    :header="$t('dashboard.modal.title')"
  >
    <PrimeForm @submit="handleSubmit()">
      <!-- Form Content -->
      <div class="form-scroll-area">
        <div class="form-grid">
          <!-- First Row -->
          <FormInputText
            id="protocol-name"
            v-model="transcriptionSettings.name"
            label="dashboard.modal.protocol_name"
            placeholder="dashboard.modal.protocol_name_placeholder"
          />
          <FormSelect
            id="language"
            v-model="transcriptionSettings.language"
            label="dashboard.modal.language"
            :options="languageOptions"
          />

          <!-- Second Row -->
          <FormSelect
            id="speakers"
            v-model="transcriptionSettings.speakers"
            v-model:error="speakerError"
            label="dashboard.modal.speakers"
            placeholder="dashboard.modal.speakers"
            :options="speakerOptions"
            :virtual-scroller-options="{ itemSize: 38 }"
          />
          <FormSelect
            id="folder"
            v-model="transcriptionSettings.folder"
            label="dashboard.modal.folder"
            :options="folderOptions"
            placeholder="dashboard.settings.folder.placeholder"
          />

          <!-- Third Row -->
          <FormSelect
            id="structure"
            v-model="transcriptionSettings.structure"
            label="dashboard.modal.structure"
            :options="structureOptions"
          />
          <FormSelect
            id="design"
            v-model="transcriptionSettings.design"
            label="dashboard.modal.design"
            :options="designOptions"
          />

          <!-- Fourth Row -->
          <FormBinaryRadioSwitch
            id="transcribe"
            v-model="transcriptionSettings.transcribe"
            label="dashboard.modal.transcript"
            :options="transcribeOptions"
          />
          <FormSelect
            v-if="transcriptionSettings.transcribe"
            id="language"
            v-model="transcriptionSettings.transcriptType"
            label="dashboard.modal.transcript_type"
            :options="transcriptTypeOptions"
          />
        </div>
      </div>

      <!-- Footer with Submit Button (must be inside form for validation) -->
      <div class="form-footer">
        <PrimeButton class="submit-button" type="submit">
          {{ $t('dashboard.modal.save_settings') }}
          <Icon name="mdi:content-save-outline" class="ml-2" />
        </PrimeButton>
      </div>
    </PrimeForm>
  </UIModal>
</template>

<script lang="ts" setup>
  import type {
    TranscriptionSettings,
    UploadedFile
  } from '~/types/Transcription';
  import { useFormOptions } from '~/utils/formOptionsFileSettings';
  import { useI18n } from 'vue-i18n';
  import { useUserStore } from '~/stores/user';

  interface Props {
    showTranscriptionModal: boolean;
    modelValue: TranscriptionSettings;
    uploadedFile?: UploadedFile;
  }

  interface Emits {
    (_e: 'submit'): void;
    (_e: 'update:modelValue', _value: TranscriptionSettings): void;
    (_e: 'update:showTranscriptionModal', _value: boolean): void;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<Emits>();

  const { t } = useI18n();
  const userStore = useUserStore();

  const folderOptions = computed(() => {
    const options: Array<{ label: string; value: string }> = [];
    userStore.userFolders.forEach((folder) => {
      options.push({ label: folder, value: folder });
    });

    if (options.length == 0) {
      options.push({
        label: t('dashboard.settings.folder.none'),
        value: 'none'
      });
    }
    return options;
  });

  const speakerOptions = ref(
    Array.from({ length: 100 }, (_, i) => ({
      label: t('dashboard.settings.speakers.speaker', { n: i }),
      value: i
    }))
  );
  speakerOptions.value[0] = {
    label: t('dashboard.settings.speakers.na'),
    value: 0
  };

  // Handle dialog visibility
  const dialogVisible = computed({
    get: () => props.showTranscriptionModal,
    set: (value) => emit('update:showTranscriptionModal', value)
  });

  // Use computed for two-way binding with parent
  const transcriptionSettings = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  });

  const {
    languageOptions,
    structureOptions,
    designOptions,
    transcribeOptions,
    transcriptTypeOptions
  } = useFormOptions();

  const speakerError = ref<string | null>(null);
  const handleSubmit = () => {
    if (!props.modelValue.speakers) {
      speakerError.value = t('form.required');
      return;
    }
    emit('submit');
  };
</script>

<style lang="scss" scoped>
  @use '@/assets/styles/theme' as *;
  @use '@/assets/styles/utils' as *;
  @use '@/assets/styles/colors' as *;
</style>
