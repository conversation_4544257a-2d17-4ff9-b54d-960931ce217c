<template>
  <UIGlassWrapper class="p-0!">
    <PrimeFileUpload
      ref="fileUploadRef"
      mode="advanced"
      :auto="true"
      :multiple="false"
      accept="audio/*, .ds2"
      :max-file-size="200000000"
      :invalid-file-type-message="$t('dashboard.filetype_error')"
      :invalid-file-size-message="$t('dashboard.filesize_error')"
      :custom-upload="true"
      :pt="{
        root: { class: 'h-full! w-full!' },
        header: { class: 'hidden!' },
        pcMessage: { root: { class: 'error' } },
        content: { class: 'border-0! py-0! h-full! w-full!' },
        empty: { class: 'h-full! w-full!' }
      }"
      @select="onFileSelect"
    >
      <template #content="{ files }">
        <div
          v-if="files.length > 0"
          class="flex flex-col gap-12! items-center! p-auto!"
        >
          <h2>{{ $t('dashboard.uploading') }}</h2>
          <LoadingSpinner />
        </div>
      </template>
      <template #empty>
        <PrimeButton
          class="w-full h-full! flex flex-col! justify-center! gap-3! text-center! p-0!"
          @click="openFileDialog"
        >
          <div>
            <Icon
              name="mdi:cloud-upload-outline"
              class="mx-auto mb-2 text-gray-400"
              size="72"
            />
            <h3>{{ $t('dashboard.upload_audio') }}</h3>
          </div>
          <div>
            <legend>{{ $t('dashboard.size_limit') }}</legend>
            <legend>{{ $t('dashboard.formats') }}</legend>
          </div>
          <div class="hidden flex flex-col items-center gap-4">
            <div class="rounded-lg! bg-[#FFFFFF26]! py-0! px-2!">
              {{ $t('dashboard.or') }}
            </div>
            <PrimeButton class="filled-button m-auto! w-auto!">
              <Icon name="mdi:microphone" />
              {{ $t('dashboard.start_recording') }}
            </PrimeButton>
          </div>
        </PrimeButton>
      </template>
    </PrimeFileUpload>
  </UIGlassWrapper>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import type { FileUploadSelectEvent } from 'primevue';
  import type FileUpload from 'primevue/fileupload';

  interface UploadedFile {
    file: File;
    [key: string]: unknown;
  }

  const emit = defineEmits(['upload']);

  const fileUploadRef = ref<InstanceType<typeof FileUpload> | null>(null);
  const uploadedFile = ref<UploadedFile | null>(null);

  const onFileSelect = async (event: FileUploadSelectEvent) => {
    const file = event.files[0];
    let url = '';
    if (file) {
      if (file.type.startsWith('audio')) {
        // Calculate duration
        file.duration = Math.floor((await getAudioDuration(file)) / 60) + 1;
        url = URL.createObjectURL(file);
      } else {
        file.type = file.name.split('.').pop();
        file.duration = 0;
      }

      uploadedFile.value = {
        file,
        name: file.name,
        size: file.size,
        type: file.type,
        duration: file.duration,
        url: url
      };

      emit('upload', uploadedFile.value);
    }
  };

  function getAudioDuration(file: File): Promise<number> {
    return new Promise((resolve, reject) => {
      const audio = new Audio();
      audio.src = URL.createObjectURL(file);

      audio.addEventListener('loadedmetadata', () => {
        resolve(audio.duration);
        URL.revokeObjectURL(audio.src);
      });

      audio.addEventListener('error', (_e) => {
        reject(new Error('Failed to load audio'));
      });
    });
  }

  const openFileDialog = () => {
    if (fileUploadRef.value) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (fileUploadRef.value as any).choose();
    } else {
      console.warn('FileUpload ref not available yet.');
    }
  };
</script>

<style lang="scss">
  @use '@/assets/styles/theme' as *;
  @use '@/assets/styles/utils' as *;
  @use '@/assets/styles/colors' as *;

  .error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    width: 100%;
    border: 0;
    outline: none !important;
    outline-width: 0 !important;
    outline-color: transparent !important;
    outline-style: none !important;

    color: $red-salmon !important;

    .p-message-content {
      .p-message-text {
        color: $red-salmon !important;
      }
    }
  }

  :deep(.p-fileupload-advanced) {
    height: 100% !important;
    background: rgba(0, 255, 0, 0.1);
  }
</style>
