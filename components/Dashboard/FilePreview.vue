<template>
  <UIGlassWrapper>
    <template #header>
      <div class="flex flex-row justify-between items-center w-full">
        <UIButton variant="text" @click="removeAudio">
          <Icon name="mdi:arrow-left" />
        </UIButton>
        <div v-show="uploadedFile.type !== 'ds2'" class="md:hidden gap-2">
          <UIButton v-if="!playing" variant="text" @click="playAudio">
            <Icon name="mdi:play" />
          </UIButton>
          <UIButton v-if="playing" variant="text" @click="pauseAudio">
            <Icon name="mdi:pause" />
          </UIButton>
          <UIButton variant="text" @click="removeAudio">
            <Icon name="mdi:delete" />
          </UIButton>
        </div>
      </div>
    </template>
    <UICard center mobile-hide class="max-w-3xl! w-full! p-4!">
      <div class="relative w-full flex">
        <Icon name="mdi:file-music-outline" class="main-icon z-10" />

        <div
          v-show="uploadedFile.type !== 'ds2'"
          class="absolute right-0 gap-2 hidden md:flex!"
        >
          <UIButton v-show="!playing" variant="text" @click="playAudio">
            <Icon name="mdi:play" />
          </UIButton>
          <UIButton v-show="playing" variant="text" @click="pauseAudio">
            <Icon name="mdi:pause" />
          </UIButton>
          <UIButton variant="text" @click="removeAudio">
            <Icon name="mdi:delete" />
          </UIButton>
        </div>
      </div>

      <h2 class="text-xl! md:text-2xl! truncate max-w-full">
        {{ uploadedFile.name }}
      </h2>

      <div>
        <legend>
          {{ $t('dashboard.upload_size') }}:
          {{ formatFileSize(uploadedFile.size) }}
        </legend>
        <legend>
          {{ $t('dashboard.length', { minutes: uploadedFile.duration ?? 0 }) }}
        </legend>
      </div>
    </UICard>
    <template #footer>
      <UIButton varaint="filled" @click="emit('transcribe')">
        {{ $t('dashboard.start_transcription') }}
        <Icon name="mdi:waveform" class="ml-2" />
      </UIButton>
    </template>
  </UIGlassWrapper>
</template>

<script lang="ts" setup>
  import { useToast } from 'primevue/usetoast';

  const toast = useToast();

  const props = defineProps({
    uploadedFile: {
      type: Object,
      required: true
    }
  });

  const emit = defineEmits(['remove', 'transcribe']);

  const playing = ref(false);
  const audio = ref<HTMLAudioElement | null>(null);

  const playAudio = () => {
    // Check if we have the original file object
    if (props.uploadedFile.type === 'ds2') return;
    if (props.uploadedFile?.file) {
      audio.value = new Audio(props.uploadedFile.url);
      audio.value.play();
      playing.value = true;
    } else if (
      props.uploadedFile?.url &&
      props.uploadedFile.url.startsWith('blob:')
    ) {
      // If we already have a blob URL, use it directly
      audio.value = new Audio(props.uploadedFile.url);
      audio.value.play();
      playing.value = true;
    } else {
      toast.add({
        severity: 'warn',
        summary: 'Warning',
        detail: 'No audio file found',
        life: 3000
      });
    }
  };

  const pauseAudio = () => {
    if (audio.value) {
      audio.value.pause();
      playing.value = false;
    }
  };

  const removeAudio = () => {
    if (audio.value) {
      audio.value.pause();
      audio.value = null;
    }
    emit('remove');
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };
</script>

<style lang="scss" scoped>
  @use '@/assets/styles/theme' as *;
  @use '@/assets/styles/utils' as *;
  @use '@/assets/styles/colors' as *;

  .main-icon {
    width: 4rem;
    height: 4rem;
    margin-inline: auto;

    @media (max-width: 768px) {
      width: 6rem;
      height: 6rem;
    }

    @media (min-height: 42rem) and (max-height: 50rem) {
      width: 3rem;
      height: 3rem;
    }
  }
</style>
