<template>
  <UIGlassWrapper center>
    <h2>{{ $t('dashboard.transcribing') }}</h2>
    <div v-if="transcriptionStore.estimatedTime" class="text-sm text-gray-400">
      {{
        $t('dashboard.estimated_time', {
          minutes: transcriptionStore.estimatedTime
        })
      }}
    </div>
    <div v-if="transcriptionStore.progress > 0" class="w-full max-w-md">
      <PrimeProgressBar
        class="h-6! rounded-2xl!"
        :value="transcriptionStore.progress"
        :show-value="true"
      />
    </div>
  </UIGlassWrapper>
</template>

<script lang="ts" setup>
  defineProps({
    transcriptionStore: {
      type: Object,
      required: true
    }
  });
</script>

<style></style>
