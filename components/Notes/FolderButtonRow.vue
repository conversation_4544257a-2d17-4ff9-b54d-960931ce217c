<template>
  <div class="hidden md:flex! flex-row gap-4 overflow-x-auto! pb-2">
    <div class="flex gap-4 overflow-x-auto">
      <!-- Create Folder -->
      <div class="card-container">
        <UICard
          class="flex flex-row items-center gap-2 card-hover"
          @click="showModal = true"
        >
          <Icon name="mdi:plus" />
          <span>{{ $t('saved_notes.create_folder') }}</span>
        </UICard>
      </div>
      <!-- Folder Items -->
      <div
        v-for="(folder, index) in folders"
        :key="index"
        class="card-container"
      >
        <UICard
          :class="{
            'card-active': folder === modelValue,
            'card-hover': folder !== modelValue
          }"
          @click="handleClick(folder)"
        >
          {{ folder }}
        </UICard>
      </div>
    </div>
    <UIModal
      v-model:visible="showModal"
      :header="$t('saved_notes.create_folder')"
    >
      <PrimeForm @submit="handleSubmit()">
        <FormInputText
          id="new_folder"
          v-model="newFolderName"
          v-model:error="error"
          label="saved_notes.folder_name"
          placeholder="saved_notes.folder_name_placeholder"
        />
        <div class="form-footer">
          <UIButton variant="submit">{{
            $t('saved_notes.folder_create')
          }}</UIButton>
        </div>
      </PrimeForm>
    </UIModal>
  </div>
</template>

<script lang="ts" setup>
  import { useI18n } from 'vue-i18n';

  const modelValue = defineModel<string | null>();

  defineProps({
    folders: {
      type: Array as PropType<string[]>,
      required: true
    }
  });

  const showModal = ref(false);
  const newFolderName = ref('');
  const { t } = useI18n();
  const error = ref<string | null>(null);
  const userStore = useUserStore();

  function handleClick(folder: string) {
    if (modelValue.value === folder) {
      modelValue.value = null;
      return;
    }
    modelValue.value = folder;
  }

  async function handleSubmit() {
    if (newFolderName.value == '') {
      error.value = t('form.required');
      return;
    }
    const response = await $fetch<{ folder: string }>('/api/folder', {
      method: 'POST',
      body: { name: newFolderName.value },
      headers: {
        Authorization: `Bearer ${userStore.token}`
      }
    });

    if (!response.folder) {
      console.error('Folder creation failed on the Server');
      return;
    }
    userStore.loadUserNotes();
    newFolderName.value = '';
    showModal.value = false;
  }
</script>

<style lang="scss" scoped>
  @use '@/assets/styles/theme' as *;
  @use '@/assets/styles/utils' as *;
  @use '@/assets/styles/colors' as *;

  .card-hover:hover {
    background-color: $backdrop-5 !important;
    border-radius: 0.75rem !important;
    box-shadow: 0 0 2px 1px $backdrop-2 !important;
    cursor: pointer;
  }

  .card-active {
    background-color: $white !important;
    color: $black !important;

    &:hover {
      background-color: $backdrop-1 !important;
      color: $black !important;
      cursor: pointer;
    }
  }

  .card-container {
    min-width: 10rem;
    height: 5rem;
    flex-shrink: 0;
  }
</style>
