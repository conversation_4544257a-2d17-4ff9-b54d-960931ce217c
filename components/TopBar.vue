<template>
  <div v-if="user" class="pl-4 pr-4 py-2 hidden md:block!">
    <div class="flex flex-row justify-between items-center w-full">
      <span class="text-2xl flex-5!">{{ $t('topbar.greet', { name: user?.profile?.firstName }) }}
      </span>
      <SubscriptionTotals :user="user" />
    </div>
    <PrimeDivider class="light-divider" />
  </div>
</template>

<script setup lang="ts">
import { useUser } from '~/composables/useUser';

const { user } = useUser();
</script>

<style lang="scss" scoped>
@use '@/assets/styles/theme' as *;
@use '@/assets/styles/utils' as *;
@use '@/assets/styles/colors' as *;
</style>
