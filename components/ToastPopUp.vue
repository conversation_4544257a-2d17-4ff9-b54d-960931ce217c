<template>
  <PrimeToast
    :pt="{
      message: ({ props }) => ({
        class: {
          'toast-success': props.message?.severity === 'success',
          'toast-error': props.message?.severity === 'error',
          'toast-info': props.message?.severity === 'info'
        }
      }),
      messageIcon: ({ props }) => ({
        class: {
          'toast-success-icon': props.message?.severity === 'success',
          'toast-error-icon': props.message?.severity === 'error',
          'toast-info-icon': props.message?.severity === 'info'
        }
      }),
      summary: ({ props }) => ({
        class: {
          'toast-success-summary': props.message?.severity === 'success',
          'toast-error-summary': props.message?.severity === 'error',
          'toast-info-summary': props.message?.severity === 'info'
        }
      }),
      detail: ({ props }) => ({
        class: {
          'toast-success-detail': props.message?.severity === 'success',
          'toast-error-detail': props.message?.severity === 'error',
          'toast-info-detail': props.message?.severity === 'info'
        }
      }),
      closeButton: ({ props }) => ({
        class: {
          'toast-success-close': props.message?.severity === 'success',
          'toast-error-close': props.message?.severity === 'error',
          'toast-info-close': props.message?.severity === 'info'
        }
      }),
      closeIcon: ({ props }) => ({
        class: {
          'toast-success-close-icon': props.message?.severity === 'success',
          'toast-error-close-icon': props.message?.severity === 'error',
          'toast-info-close-icon': props.message?.severity === 'info'
        }
      })
    }"
  />
</template>

<script lang="ts" setup>
  // No additional setup needed - PrimeToast connects to the toast service automatically
</script>

<style lang="scss">
  @use '@/assets/styles/theme' as *;
  @use '@/assets/styles/utils' as *;
  @use '@/assets/styles/colors' as *;
  @use '@/assets/styles/toast' as *;
</style>
