<template>
  <div v-if="pageCount && pageCount > 0" class="ui-table-pagination">
    <UIButton
      variant="text"
      :disabled="page === 0"
      @click="$emit('prev', $event)"
    >
      <Icon name="mdi:chevron-left" />
    </UIButton>

    <div class="pagination-pages">
      <template
        v-for="pageNum in getVisiblePages(page, pageCount)"
        :key="pageNum"
      >
        <UIButton
          class="pagination-page"
          variant="text"
          :class="{ active: (pageNum as number) - 1 === page }"
          :disabled="pageNum === '...' || pageNum - 1 === page"
          @click="$emit('goto', (pageNum as number) - 1)"
        >
          {{ pageNum }}
        </UIButton>
      </template>
    </div>

    <UIButton
      variant="text"
      :disabled="page === pageCount - 1"
      @click="$emit('next', $event)"
    >
      <Icon name="mdi:chevron-right" />
    </UIButton>
  </div>
</template>

<script lang="ts" setup>
  defineProps<{ page: number; pageCount: number | undefined }>();
  defineEmits<{
    (_e: 'prev' | 'next', _event: Event): void;
    (_e: 'goto', _pageNum: number): void;
  }>();
  const getVisiblePages = (
    currentPage: number,
    totalPages: number
  ): (number | '...')[] => {
    const pages: (number | '...')[] = [];
    const delta = 2;
    const rangeStart = Math.max(2, currentPage - delta + 1);
    const rangeEnd = Math.min(totalPages - 1, currentPage + delta + 1);

    pages.push(1);

    if (rangeStart > 2) pages.push('...');
    for (let i = rangeStart; i <= rangeEnd; i++) pages.push(i);
    if (rangeEnd < totalPages - 1) pages.push('...');
    if (totalPages > 1) pages.push(totalPages);

    return pages;
  };
</script>

<style scoped lang="scss">
  @use '@/assets/styles/theme' as *;
  @use '@/assets/styles/colors' as *;

  .ui-table-pagination {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    gap: 0.5rem;
    padding: 0;
  }
</style>
