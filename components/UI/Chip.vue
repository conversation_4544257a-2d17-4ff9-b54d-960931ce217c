<template>
  <PrimeChip
    :label="label"
    :icon="icon"
    :image="image"
    :removable="removable"
    :class="variant + '-chip'"
    v-bind="$attrs"
    @remove="$emit('remove', $event)"
  >
    <slot />
  </PrimeChip>
</template>

<script lang="ts" setup>
  defineProps({
    label: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      default: ''
    },
    image: {
      type: String,
      default: ''
    },
    removable: {
      type: Boolean,
      default: false
    },
    variant: {
      type: String,
      default: 'default',
      validator: (value: string) =>
        [
          'default',
          'primary',
          'secondary',
          'success',
          'warning',
          'danger'
        ].includes(value)
    }
  });

  defineEmits(['remove']);
</script>

<style lang="scss" scoped>
  @use '@/assets/styles/theme' as *;
  @use '@/assets/styles/colors' as *;

  .default-chip {
    background: rgba(255, 255, 255, 0.1);
    color: $text-white;
  }

  .primary-chip {
    background: rgba(139, 92, 246, 0.2);
    border: 1px solid rgba(139, 92, 246, 0.3);
    color: $text-white;
  }

  .secondary-chip {
    background: $backdrop-3;
    color: $text-white;
  }

  .success-chip {
    background: rgba(34, 197, 94, 0.2);
    border: 1px solid rgba(34, 197, 94, 0.3);
    color: $text-white;
  }

  .warning-chip {
    background: rgba(251, 191, 36, 0.2);
    border: 1px solid rgba(251, 191, 36, 0.3);
    color: $text-white;
  }

  .danger-chip {
    background: rgba(239, 68, 68, 0.2);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: $text-white;
  }
</style>
