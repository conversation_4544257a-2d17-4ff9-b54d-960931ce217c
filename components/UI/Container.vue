<template>
  <div class="container" :class="'justify-' + justify">
    <div v-if="$slots.header" class="header">
      <slot name="header" />
    </div>
    <div
      v-if="$slots.default"
      class="content"
      :class="'overflow-y-' + contentOverflow"
    >
      <slot />
    </div>
    <div v-if="$slots.footer" class="footer">
      <slot name="footer" />
    </div>
  </div>
</template>

<script lang="ts" setup>
  defineProps({
    justify: {
      type: String,
      default: 'center'
    },
    contentOverflow: {
      type: String,
      default: 'hidden'
    }
  });
</script>

<style lang="scss" scoped>
  @use '@/assets/styles/theme' as *;
  @use '@/assets/styles/utils' as *;
  @use '@/assets/styles/colors' as *;

  .container {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    gap: 1rem;
    padding: 2rem 1rem;
    max-width: none;

    @media (min-width: 768px) {
      padding: 0 2rem;
    }
  }

  .header,
  .footer {
    flex-shrink: 0;
  }

  .content {
    flex-grow: 1;
    width: 100%;
  }
</style>
