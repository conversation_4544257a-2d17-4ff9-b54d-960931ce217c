<template>
  <div class="search-wrapper">
    <div class="search-container">
      <Icon name="mdi:magnify" class="search-icon" />
      <PrimeInputText
        v-model="searchValue"
        :placeholder="placeholder"
        class="search-input"
        @input="handleInput"
        @keyup.enter="handleEnter"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
  defineProps<{
    placeholder?: string;
  }>();

  const emit = defineEmits<{
    'update:modelValue': [value: string];
    search: [value: string];
  }>();

  const searchValue = defineModel<string>();

  const handleInput = () => {
    emit('search', searchValue.value || '');
  };

  const handleEnter = () => {
    emit('search', searchValue.value || '');
  };
</script>

<style lang="scss" scoped>
  @use '@/assets/styles/theme' as *;
  @use '@/assets/styles/utils' as *;
  @use '@/assets/styles/colors' as *;

  .search-wrapper {
    display: flex;
    gap: 1rem;
    align-items: center;
    width: 100%;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 0.75rem;
    }
  }

  .search-container {
    position: relative;
    flex: 1;
    min-width: 0;
    width: 100%;
  }

  .search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: $text-dark;
    z-index: 1;
    width: 1.25rem;
    height: 1.25rem;
  }

  .search-input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 3rem !important;
    background: $backdrop-5 !important;
    border: 1px solid $backdrop-3 !important;
    border-radius: 0.5rem !important;
    color: $text-white !important;
    font-size: 1rem;
    transition: all 0.2s ease;

    &::placeholder {
      color: $text-dark !important;
    }

    &:focus {
      outline: none !important;
      background: $backdrop-3 !important;
    }

    &:hover {
      border-color: $hover-gray-solid !important;
    }
  }

  .filter-container {
    display: flex;
    gap: 0.5rem;
    flex-shrink: 0;

    @media (max-width: 768px) {
      width: 100%;
      justify-content: space-between;
    }
  }
</style>
