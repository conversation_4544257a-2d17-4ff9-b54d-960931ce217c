<template>
  <PrimePopover ref="popoverRef" append-to="body" class="popoverlay">
    <slot />
  </PrimePopover>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';

  const popoverRef = ref();

  defineExpose({
    toggle: (event: MouseEvent) => popoverRef.value?.toggle(event),
    show: (event: MouseEvent) => popoverRef.value?.show(event),
    hide: () => popoverRef.value?.hide()
  });

  export interface UIPopOverListRef {
    toggle: (_event: MouseEvent) => void;
    show: (_event: MouseEvent) => void;
    hide: () => void;
  }
</script>

<style lang="scss">
  @use '@/assets/styles/theme' as *;
  @use '@/assets/styles/colors' as *;

  .popoverlay {
    @extend .overlay;
    border-radius: 0.5rem !important;
  }

  .popover-label {
    @extend .options-label;
    font-size: 1.125rem;
    padding: 0.75rem !important;
    cursor: pointer;
  }
</style>
