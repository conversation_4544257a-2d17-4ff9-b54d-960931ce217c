<template>
  <PrimeSelect
    v-model="modelValue"
    :options="options"
    option-label="label"
    option-value="value"
    :placeholder="placeholder"
    :show-clear="showClear"
    class="form-select"
    :pt="{
      overlay: {
        class: 'dropdown-overlay'
      },
      clearIcon: { class: 'end-8!' },
      option: { class: 'options-label' }
    }"
    :append-to="appendTo"
  />
</template>

<script lang="ts" setup>
  const modelValue = defineModel<string | number | null>();

  defineProps<{
    options: Array<{ label: string; value: string | number }>;
    placeholder?: string;
    appendTo?: string;
    showClear?: boolean;
  }>();
</script>

<style lang="scss" scoped>
  @use '@/assets/styles/theme' as *;
  @use '@/assets/styles/colors' as *;
</style>
