<template>
  <div class="main-card">
    <div v-if="$slots.header" class="header">
      <slot name="header" />
    </div>
    <div
      v-if="$slots.default"
      class="content"
      :class="{ 'items-center gap-4': center }"
    >
      <slot />
    </div>
    <div v-if="$slots.footer" class="footer">
      <slot name="footer" />
    </div>
  </div>
</template>

<script lang="ts" setup>
  defineProps({
    center: {
      type: Boolean,
      required: false,
      default: false
    }
  });
</script>

<style lang="scss" scoped>
  @use '@/assets/styles/theme' as *;
  @use '@/assets/styles/utils' as *;
  @use '@/assets/styles/colors' as *;

  .main-card {
    @extend .card;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 2rem;
    gap: 1rem;
    height: 100%;
    max-width: none;
    overflow: auto;
    padding-bottom: 1.5rem;
  }

  .header {
    flex-shrink: 0;
  }

  .footer {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
  }

  .content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    flex-grow: 1;
    width: 100%;
  }
</style>
