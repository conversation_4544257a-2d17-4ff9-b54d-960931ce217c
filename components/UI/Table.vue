<template>
  <div ref="tableWrapper">
    <PrimeDataTable
      v-bind="$attrs"
      :value="items"
      :paginator="paginator"
      :rows="rows"
      :loading="loading"
      :scrollable="scrollable"
      :total-records="totalRecords"
      scroll-height="flex"
      :pt="{
        root: { class: 'ui-data-table' },
        header: { class: 'ui-data-table-header' },
        bodyRow: { class: 'ui-data-table-body-row' },
        headerRow: { class: 'ui-data-table-header-row' },
        footer: { class: 'ui-data-table-footer' },
        emptyMessageCell: { class: 'border-0!' }
      }"
      @page="onPageChange"
    >
      <template #header>
        <slot name="header" />
      </template>
      <template #empty>
        <slot name="empty" />
      </template>
      <template #footer>
        <slot name="footer" />
      </template>
      <template
        #paginatorcontainer="{
          page,
          pageCount,
          prevPageCallback,
          nextPageCallback,
          changePageCallback
        }"
      >
        <UIPagination
          :page="page"
          :page-count="pageCount"
          @prev="prevPageCallback"
          @next="nextPageCallback"
          @goto="changePageCallback"
        />
      </template>
      <slot />
    </PrimeDataTable>
  </div>
</template>

<script lang="ts" setup>
  defineProps({
    items: {
      type: Array,
      required: true
    },
    paginator: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    },
    scrollable: {
      type: Boolean,
      default: false
    },
    rows: {
      type: Number,
      default: 5
    },
    totalRecords: {
      type: Number,
      default: 0
    }
  });
  const emit = defineEmits(['page']);
  function onPageChange() {
    emit('page');
  }
</script>

<style lang="scss">
  @use '@/assets/styles/theme' as *;
  @use '@/assets/styles/colors' as *;

  .ui-table-wrapper {
    flex: 1 1 auto;
    background: inherit;
  }

  .ui-data-table {
    background: transparent;
  }

  thead {
    position: relative !important;
  }

  .ui-data-table-header-row {
    th {
      border: none !important;
      padding: 0.5rem 1rem !important;
      font-weight: 700;
      font-size: large;
    }
  }

  .ui-data-table-body-row {
    border-radius: 0.5rem !important;

    &:hover {
      background: $backdrop-5 !important;
    }

    td {
      border: none !important;
      padding: 1rem !important;

      background-color: $backdrop-5;

      &:first-child {
        border-top-left-radius: 0.5rem !important;
        border-bottom-left-radius: 0.5rem !important;
      }

      &:last-child {
        border-top-right-radius: 0.5rem !important;
        border-bottom-right-radius: 0.5rem !important;
      }
    }
  }

  .ui-data-table-header,
  .ui-data-table-footer {
    border: none !important;
  }

  .p-datatable-table {
    border-spacing: 0 0.25rem !important;
  }
</style>
