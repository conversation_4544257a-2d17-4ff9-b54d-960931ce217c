<template>
  <div
    class="card"
    :class="{
      'flex! flex-col! items-center! text-center!': center,
      'hidden-card': mobileHide
    }"
  >
    <slot />
  </div>
</template>

<script lang="ts" setup>
  defineProps({
    center: {
      type: Boolean,
      required: false,
      default: false
    },
    mobileHide: {
      type: Boolean,
      required: false,
      default: false
    }
  });
</script>

<style lang="scss" scoped>
  .hidden-card {
    @media (max-width: 768px) {
      width: 100%;
      padding: 0;
      background: transparent;
      border: 0;
    }
  }
</style>
