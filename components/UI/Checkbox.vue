<template>
  <PrimeCheckbox
    :model-value="modelValue"
    :binary="binary"
    :class="variant + '-checkbox'"
    @update:model-value="emit('update:modelValue', $event)"
  />
</template>

<script lang="ts" setup>
  defineProps<{
    modelValue: boolean;
    binary?: boolean;
    variant?: string;
  }>();

  const emit = defineEmits(['update:modelValue']);
</script>

<style lang="scss">
  @use '@/assets/styles/theme' as *;
  @use '@/assets/styles/colors' as *;

  .primary-checkbox {
    .p-checkbox-box {
      width: 1.25rem !important;
      height: 1.25rem !important;
      border: 2px solid $backdrop-3;
      border-radius: 0.25rem;
      background: transparent;
      transition: all 0.2s;

      &.p-highlight {
        background: $purple-primary !important;
        border-color: $purple-primary !important;
      }
    }
  }
</style>
