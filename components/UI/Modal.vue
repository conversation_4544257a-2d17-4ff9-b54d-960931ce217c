<template>
  <PrimeDialog
    v-model:visible="dialogVisible"
    modal
    :header="header"
    append-to="main"
    :closable="true"
    :dismissable-mask="true"
    :pt="{
      root: { class: 'modal' },
      header: { class: 'modal-header-container' },
      content: { class: 'modal-content-container overflow-hidden!' },
      footer: { class: 'modal-footer-container' }
    }"
  >
    <template #closebutton>
      <PrimeButton class="text-button" @click="dialogVisible = false">
        <Icon name="mdi:close" />
      </PrimeButton>
    </template>
    <slot />

    <template #footer>
      <slot name="footer" />
    </template>
  </PrimeDialog>
</template>

<script lang="ts" setup>
  const props = defineProps({
    visible: {
      type: Boolean,
      required: true
    },
    header: {
      type: String,
      required: true
    }
  });

  const emit = defineEmits(['update:visible']);

  const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
  });
</script>

<style lang="scss">
  @use '@/assets/styles/theme' as *;
  @use '@/assets/styles/utils' as *;
  @use '@/assets/styles/colors' as *;

  .modal {
    position: relative;
    background-color: $purple-deep !important;
    border-radius: 1rem !important;
    padding: 2.5rem;
    width: 42dvw;
    box-shadow: 0 0 64px 16px rgba(0, 255, 255, 0.2) !important;

    @media screen and (max-width: 1268px) {
      width: 56dvw;
      padding: 1.5rem;
    }

    @media screen and (max-width: 1024px) {
      width: 64dvw;
      padding: 1.5rem;
    }

    @media screen and (max-width: 768px) {
      width: 100dvw;
      padding: 2rem;
      max-height: 100% !important;
      border-radius: 0 !important;
      height: 100%;
    }
  }

  .modal-header-container {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 1rem;

    @media screen and (max-width: 768px) {
      font-size: 1.5rem;
    }
  }

  .modal-content-container {
    height: 100%;
    display: flex;
    flex-direction: column;

    form {
      display: flex;
      flex-direction: column;
      flex: 1 1 auto;
      overflow: hidden;

      .form-scroll-area {
        @extend .overflow-y-auto;
        flex: 1 1 auto;
        padding: 0;
      }

      .form-footer {
        flex-shrink: 0;
        padding-top: 1rem;
        display: flex;
        justify-content: center;
        align-items: center;

        @media screen and (max-width: 768px) {
          button {
            font-size: 1rem;
          }
        }
      }
    }
  }

  .modal-footer-container {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding-top: 1rem;
  }

  main .p-dialog-mask {
    position: absolute !important;
    background: $backdrop-black-2 !important;
    backdrop-filter: blur(12px) !important;
    z-index: 1000 !important;
  }
</style>
