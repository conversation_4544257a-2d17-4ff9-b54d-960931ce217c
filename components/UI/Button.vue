<template>
  <PrimeButton
    :class="variant + '-button'"
    :type="variant === 'submit' ? 'submit' : 'button'"
  >
    <slot />
  </PrimeButton>
</template>

<script lang="ts" setup>
  defineProps({
    variant: {
      type: String,
      default: 'filled',
      validator: (value: string) =>
        ['text', 'filled', 'logout', 'icon', 'submit', 'action'].includes(value)
    }
  });
</script>

<style lang="scss" scoepd>
  @use '@/assets/styles/theme' as *;
  @use '@/assets/styles/colors' as *;

  button {
    font-size: 1.125rem;
    font-weight: 700;
    border-radius: 5px;
  }

  .filled-button {
    background: $white !important;
    color: $text-black !important;
    padding: 0.5rem 1rem !important;
    border-radius: 2rem !important;

    &:hover {
      background: $light-gray !important;
    }

    span {
      color: $black !important;
    }
  }

  .text-button {
    border-radius: 0.5rem !important;
    padding: 0.5rem 1rem !important;

    span {
      width: 2rem;
      height: 2rem;
    }

    &:hover {
      background-color: $backdrop-3 !important;
    }

    &:disabled {
      &:hover {
        background-color: inherit !important;
      }

      span {
        color: $backdrop-2 !important;
      }
    }

    &.active {
      background: $white !important;
      color: $text-black !important;
      padding: 0.5rem 1rem !important;
      border-radius: 0.5rem !important;

      span {
        color: $black;
      }

      &:disabled {
        background: $white !important;
        color: $text-black !important;
        padding: 0.5rem 1rem !important;
        border-radius: 0.5rem !important;

        cursor: alias !important;

        span {
          color: $black;
        }
      }
    }
  }

  .icon-button {
    @extend .text-button;
    padding: 0.5rem !important;
    width: 2.25rem;
    height: 2.25rem;
  }

  .action-button {
    @extend .form-select;
    font-weight: normal;
  }

  .logout-button {
    @extend .text-button;

    span {
      width: 1.25rem;
      height: 1.25rem;
    }

    &:hover {
      background-color: $backdrop-3 !important;
    }
  }

  .submit-button {
    @extend .filled-button;
    margin: auto !important;

    @media (max-width: 1024px) {
      width: 100%;
    }
  }
</style>
