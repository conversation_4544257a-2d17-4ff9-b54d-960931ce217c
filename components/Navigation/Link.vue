<template>
  <NuxtLink
    :to="localePath(to)"
    active-class="active-link"
    class="flex items-center gap-2"
  >
    <Icon :name="icon" />
    <span>{{ label }}</span>
  </NuxtLink>
</template>

<script setup>
  defineProps({
    to: {
      type: String,
      required: true
    },
    icon: {
      type: String,
      required: true
    },
    label: {
      type: String,
      required: true
    }
  });

  const localePath = useLocalePath();
</script>
