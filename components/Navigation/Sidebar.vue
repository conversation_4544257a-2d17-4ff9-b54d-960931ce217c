<template>
  <!-- <PERSON> Header -->
  <div class="lg:hidden! sticky top-0 z-40 flex! items-center p-2 pb-0 gap-2">
    <!-- Logo -->
    <div class="w-3/8 flex justify-start">
      <NuxtLink
        :to="localePath('/dashboard')"
        class="py-0! hover:bg-transparent active:bg-transparent"
      >
        <NuxtImg src="/logo_with_text.svg" class="h-10 w-auto" alt="logo" />
      </NuxtLink>
    </div>

    <!-- Get Pro Button -->
    <div class="w-3/8 flex justify-center">
      <NuxtLink :to="localePath('/settings/subscription')" class="p-0!">
        <PrimeButton
          class="filled-button h-8! get-pro-gradient px-3! py-2! w-auto! min-w-0!"
        >
          <Icon name="mdi:crown" class="h-4!" />
          <span class="text-sm!">{{ $t('subscription.get_pro') }}</span>
        </PrimeButton>
      </NuxtLink>
    </div>

    <!-- Burger Menu <PERSON>ton -->
    <div class="w-2/8 flex justify-end">
      <PrimeButton
        class="pr-2!"
        icon="mdi:menu"
        variant="text"
        @click="toggleSidebar"
      >
        <Icon name="mdi:menu" />
      </PrimeButton>
    </div>
  </div>

  <!-- Desktop Sidebar -->
  <aside class="hidden lg:flex! w-72! flex-shrink-0! h-screen flex-col">
    <div class="p-3 h-full flex flex-col">
      <NuxtImg
        src="/logo_with_text.svg"
        class="ml-0 lg:-ml-10 h-16 mb-4"
        alt="logo"
      />
      <div class="flex flex-1 flex-col justify-between!">
        <NavigationMain />
        <NavigationFooter />
      </div>
    </div>
  </aside>

  <!-- Mobile Sidebar Overlay -->
  <div
    v-if="isSidebarOpen"
    class="lg:hidden fixed inset-0 z-50 mobile-overlay animate-fade-in"
    @click="closeSidebar"
  >
    <aside
      class="absolute top-0 left-0 bottom-0 w-80 max-w-[85vw] flex flex-col bg-sidebar mobile-border-right animate-slide-in"
      @click.stop
    >
      <div class="flex items-center justify-between p-6 pb-2">
        <NuxtImg src="/logo_with_text.svg" class="h-14 w-auto" alt="logo" />
      </div>

      <div
        class="flex flex-1 flex-col justify-between! p-4 pb-0 overflow-y-auto"
      >
        <NavigationMain @navigate="closeSidebar" />
        <NavigationFooter />
      </div>
    </aside>
  </div>
</template>

<script lang="ts" setup>
  const isSidebarOpen = ref(false);
  const localePath = useLocalePath();

  const toggleSidebar = () => {
    isSidebarOpen.value = !isSidebarOpen.value;
  };

  const closeSidebar = () => {
    isSidebarOpen.value = false;
  };

  // Close sidebar on route change
  const route = useRoute();
  watch(
    () => route.path,
    () => {
      closeSidebar();
    }
  );

  // Close sidebar on escape key
  onMounted(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        closeSidebar();
      }
    };
    document.addEventListener('keydown', handleEscape);

    onUnmounted(() => {
      document.removeEventListener('keydown', handleEscape);
    });
  });
</script>

<style lang="scss" scoped>
  @use '@/assets/styles/colors' as *;
  @use '@/assets/styles/theme' as *;

  .mobile-overlay {
    background: $backdrop-black-1;
    backdrop-filter: blur(4px);
  }

  .hover-bg-backdrop:hover {
    background: $backdrop-3 !important;
  }

  .get-pro-gradient {
    background: linear-gradient(
      135deg,
      $yellow-primary,
      $yellow-dark
    ) !important;
    box-shadow: 0 2px 8px $yellow-transparent;

    &:hover {
      background: linear-gradient(
        135deg,
        $yellow-base,
        $yellow-flat
      ) !important;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px $yellow-transparent;
    }

    span {
      color: $black !important;
    }
  }

  .text-white {
    color: $text-white;
  }

  /* Animations */
  .animate-fade-in {
    animation: fadeIn 0.2s ease-out;
  }

  .animate-slide-in {
    animation: slideInLeft 0.3s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }

    to {
      opacity: 1;
    }
  }

  @keyframes slideInLeft {
    from {
      transform: translateX(-100%);
    }

    to {
      transform: translateX(0);
    }
  }
</style>
