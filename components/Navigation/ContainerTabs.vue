<template>
  <div class="block! pl-4">
    <PrimeTabs :value="route.path">
      <PrimeTabList
        :pt="{
          tabList: { class: 'border-0! flex! flex-col! gap-1!' }
        }"
      >
        <PrimeTab
          v-for="tab in items"
          :key="tab.label"
          :value="tab.route"
          class="border-0! w-full"
        >
          <NuxtLink v-slot="{ href, navigate }" :to="tab.route" custom>
            <a
              v-ripple
              :href="href"
              class="flex items-center gap-3 px-2! py-2 text-left"
              :class="{
                'active-link border-r-3! text-white!': route.path === tab.route
              }"
              @click="navigate"
            >
              <Icon :name="tab.icon" />
              <span
                class="font-normal"
                :class="{
                  dark: route.path !== tab.route
                }"
              >
                {{ tab.label }}</span
              >
            </a>
          </NuxtLink>
        </PrimeTab>
      </PrimeTabList>
    </PrimeTabs>
  </div>
</template>

<script lang="ts" setup>
  import { useRoute } from 'vue-router';

  defineProps({
    items: {
      type: Array as () => { label: string; route: string; icon: string }[],
      required: true
    }
  });
  const route = useRoute();
</script>

<style lang="scss" scoped>
  @use '@/assets/styles/theme' as *;
  @use '@/assets/styles/utils' as *;
  @use '@/assets/styles/colors' as *;
</style>
