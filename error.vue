<template>
  <main class="h-screen m-0! rounded-none! overflow-hidden!">
    <UIContainer>
      <UIGlassWrapper center class="max-w-4xl! h-fit! my-12!">
        <div class="text-4xl! flex flex-col items-center gap-4">
          <Icon class="w-32 h-32 gap-0" :name="errorIcon" />
          <h1>{{ error.statusCode }}</h1>
        </div>

        <h2>{{ errorTitle }}</h2>

        <p>{{ errorDescription }}</p>

        <div class="grid grid-cols-1 md:grid-cols-3! gap-4!">
          <PrimeButton class="filled-button p-3!" @click="goHome">
            <Icon name="mdi:home" />
            <span class="ml-1 text-sm"> {{ $t('error.actions.home') }}</span>
          </PrimeButton>

          <PrimeButton class="filled-button p-3!" @click="goBack">
            <Icon name="mdi:arrow-left" />
            <span class="ml-1 text-sm"> {{ $t('error.actions.back') }}</span>
          </PrimeButton>

          <PrimeButton class="filled-button p-3!" @click="refresh">
            <Icon name="mdi:refresh" />
            <span class="ml-1 text-sm"> {{ $t('error.actions.refresh') }}</span>
          </PrimeButton>
        </div>
        <template #footer>
          <div class="w-full border-t border-white/20 pt-6 mt-4">
            <p class="help-text text-center">{{ $t('error.help.text') }}</p>
            <NuxtLink :to="localePath('/support')" class="flex justify-center">
              <Icon name="mdi:help-circle" />
              {{ $t('error.help.contact') }}
            </NuxtLink>
          </div>
        </template>
      </UIGlassWrapper>
    </UIContainer>
  </main>
</template>

<script setup lang="ts">
  import type { NuxtError } from '#app';

  interface Props {
    error: NuxtError;
  }

  const props = defineProps<Props>();

  const { t } = useI18n();
  const localePath = useLocalePath();
  const router = useRouter();

  const errorIcon = computed(() => {
    switch (props.error.statusCode) {
      case 404:
        return 'mdi:file-question';
      case 403:
        return 'mdi:lock';
      case 500:
        return 'mdi:server-network-off';
      case 503:
        return 'mdi:wrench';
      default:
        return 'mdi:alert-circle';
    }
  });

  const errorTitle = computed(() => {
    switch (props.error.statusCode) {
      case 404:
        return t('error.404.title');
      case 403:
        return t('error.403.title');
      case 500:
        return t('error.500.title');
      case 503:
        return t('error.503.title');
      default:
        return t('error.default.title');
    }
  });

  const errorDescription = computed(() => {
    switch (props.error.statusCode) {
      case 404:
        return t('error.404.description');
      case 403:
        return t('error.403.description');
      case 500:
        return t('error.500.description');
      case 503:
        return t('error.503.description');
      default:
        return t('error.default.description');
    }
  });

  const goHome = () => {
    navigateTo(localePath('/dashboard'));
  };

  const goBack = () => {
    if (window.history.length > 1) {
      router.back();
    } else {
      goHome();
    }
  };

  const refresh = () => {
    window.location.reload();
  };

  useHead({
    title: `${props.error.statusCode} - ${errorTitle.value}`,
    meta: [{ name: 'robots', content: 'noindex' }]
  });
</script>

<style lang="scss" scoped>
  @use '@/assets/styles/theme' as *;
  @use '@/assets/styles/utils' as *;
  @use '@/assets/styles/colors' as *;

  .help-text {
    color: $text-dark;
    font-size: 0.875rem;
    margin-bottom: 1rem;
  }
</style>
