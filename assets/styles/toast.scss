@use '@/assets/styles/colors' as *;

.toast-info {
  background-color: $info-bg !important;
  border: 2px solid $info-border !important;
  border-radius: 0.5rem !important;
  padding: 0.75rem !important;
}

.toast-info-icon {
  color: $info-text !important;
  padding: 0.25rem 0.5rem !important;
}

.toast-info-summary {
  color: $info-text !important;
  font-weight: bold !important;
}

.toast-info-detail {
  color: $detail-text !important;
}

.toast-info-close {
  padding: 0.5rem !important;
}

.toast-info-close-icon {
  font-weight: 1000 !important;
  color: $info-text !important;

  &:hover {
    color: $info-hover !important;
  }
}

// ───── ERROR ─────

.toast-error {
  background-color: $error-bg !important;
  border: 2px solid $error-border !important;
  border-radius: 0.5rem !important;
  padding: 0.75rem !important;
}

.toast-error-icon {
  color: $error-text !important;
  padding: 0.25rem 0.5rem !important;
}

.toast-error-summary {
  color: $error-text !important;
  font-weight: bold !important;
}

.toast-error-detail {
  color: $detail-text !important;
}

.toast-error-close {
  padding: 0.5rem !important;
}

.toast-error-close-icon {
  font-weight: 1000 !important;
  color: $error-text !important;

  &:hover {
    color: $error-hover !important;
  }
}

// ───── SUCCESS ─────

.toast-success {
  background-color: $success-bg !important;
  border: 2px solid $success-border !important;
  border-radius: 0.5rem !important;
  padding: 0.75rem !important;
}

.toast-success-icon {
  color: $success-text !important;
  padding: 0.25rem 0.5rem !important;
}

.toast-success-summary {
  color: $success-text !important;
  font-weight: bold !important;
}

.toast-success-detail {
  color: $detail-text !important;
}

.toast-success-close {
  padding: 0.5rem !important;
}

.toast-success-close-icon {
  font-weight: 1000 !important;
  color: $success-text !important;

  &:hover {
    color: $success-hover !important;
  }
}
