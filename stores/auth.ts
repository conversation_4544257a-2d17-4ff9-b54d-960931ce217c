import { defineStore } from 'pinia';
import type { User, LoginCredentials, LoginResponse, UserImpl } from '~/types/User';

interface AuthState {
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  sessionExpiry: Date | null;
}

interface SessionData {
  user: User;
  token: string;
  refreshToken?: string;
  timestamp: number;
  isAuthenticated: boolean;
  sessionExpiry?: string;
}

export const useAuthStore = defineStore('auth', {
  state: (): AuthState => ({
    user: null,
    token: null,
    refreshToken: null,
    isAuthenticated: false,
    isLoading: false,
    sessionExpiry: null
  }),

  getters: {
    isLoggedIn: (state): boolean => 
      state.isAuthenticated && 
      state.user !== null && 
      state.token !== null,

    isSessionValid: (state): boolean => 
      state.sessionExpiry ? new Date() < state.sessionExpiry : false,

    userFullName: (state): string => 
      state.user?.fullName || '',

    userInitials: (state): string => 
      state.user?.initials || '',

    userEmail: (state): string => 
      state.user?.email || '',

    isSubscriptionActive: (state): boolean => 
      state.user?.isSubscriptionActive || false
  },

  actions: {
    // Session management
    setRedirectPath(path: string) {
      if (import.meta.client) {
        sessionStorage.setItem('redirectAfterLogin', path);
      }
    },

    getRedirectPath(): string {
      if (import.meta.client) {
        return sessionStorage.getItem('redirectAfterLogin') || '/dashboard';
      }
      return '/dashboard';
    },

    clearRedirectPath() {
      if (import.meta.client) {
        sessionStorage.removeItem('redirectAfterLogin');
      }
    },

    // User and token setters
    setUser(userData: User) {
      this.user = userData;
      this.isAuthenticated = true;
    },

    setTokens(token: string, refreshToken?: string, expiresIn?: number) {
      this.token = token;
      this.refreshToken = refreshToken || null;
      
      if (expiresIn) {
        this.sessionExpiry = new Date(Date.now() + expiresIn * 1000);
      }

      // Store token in localStorage for persistence
      if (import.meta.client) {
        localStorage.setItem('auth-token', token);
        if (refreshToken) {
          localStorage.setItem('auth-refresh-token', refreshToken);
        }
      }
    },

    // Session persistence
    persistSession() {
      if (import.meta.client && this.user && this.token) {
        const sessionData: SessionData = {
          user: this.user,
          token: this.token,
          refreshToken: this.refreshToken || undefined,
          timestamp: Date.now(),
          isAuthenticated: true,
          sessionExpiry: this.sessionExpiry?.toISOString()
        };

        const sessionDataString = JSON.stringify(sessionData);

        // Store in multiple locations for reliability
        localStorage.setItem('auth-session', sessionDataString);
        sessionStorage.setItem('auth-session', sessionDataString);
        localStorage.setItem('auth-active', 'true');

        // Store in cookies as fallback
        const { setCookie } = useCookies();
        setCookie('auth-session', sessionDataString, 7);
        setCookie('auth-token', this.token, 7);
      }
    },

    restoreSession(): boolean {
      if (!import.meta.client) return false;

      try {
        // Try sessionStorage first (current session)
        let sessionData = sessionStorage.getItem('auth-session');

        // Fallback to localStorage (persistent)
        if (!sessionData) {
          sessionData = localStorage.getItem('auth-session');
          if (sessionData) {
            sessionStorage.setItem('auth-session', sessionData);
          }
        }

        // Final fallback to cookies
        if (!sessionData) {
          const { getCookie } = useCookies();
          sessionData = getCookie('auth-session');
          if (sessionData) {
            localStorage.setItem('auth-session', sessionData);
            sessionStorage.setItem('auth-session', sessionData);
          }
        }

        if (sessionData) {
          const data: SessionData = JSON.parse(sessionData);

          // Check if session is not too old (7 days)
          const maxAge = 7 * 24 * 60 * 60 * 1000;
          if (Date.now() - data.timestamp > maxAge) {
            this.clearAuth();
            return false;
          }

          // Restore user state
          this.user = UserImpl.fromJSON(data.user);
          this.token = data.token;
          this.refreshToken = data.refreshToken || null;
          this.isAuthenticated = data.isAuthenticated;
          
          if (data.sessionExpiry) {
            this.sessionExpiry = new Date(data.sessionExpiry);
          }

          return true;
        }
      } catch (error) {
        console.error('Error restoring session:', error);
        this.clearAuth();
      }
      return false;
    },

    clearSession() {
      if (import.meta.client) {
        localStorage.removeItem('auth-session');
        localStorage.removeItem('auth-active');
        localStorage.removeItem('auth-token');
        localStorage.removeItem('auth-refresh-token');
        sessionStorage.removeItem('auth-session');

        // Clear cookies
        const { deleteCookie } = useCookies();
        deleteCookie('auth-session');
        deleteCookie('auth-token');
      }
    },

    clearAuth() {
      this.user = null;
      this.isAuthenticated = false;
      this.token = null;
      this.refreshToken = null;
      this.sessionExpiry = null;
      this.clearSession();
    },

    // Authentication actions
    async login(credentials: LoginCredentials): Promise<LoginResponse> {
      this.isLoading = true;
      try {
        const response = await $fetch<LoginResponse>('/api/login', {
          method: 'POST',
          body: credentials
        });

        if (response.success && response.user && response.token) {
          this.setUser(UserImpl.fromJSON(response.user));
          this.setTokens(response.token, response.refreshToken, response.expiresIn);
          this.persistSession();
          return response;
        } else {
          this.clearAuth();
          return response;
        }
      } catch (error) {
        console.error('Login error:', error);
        this.clearAuth();
        return {
          success: false,
          message: 'Login failed. Please try again.'
        };
      } finally {
        this.isLoading = false;
      }
    },

    async logout() {
      this.isLoading = true;
      try {
        await $fetch('/api/logout', {
          method: 'POST',
          body: {
            id: this.user?.id,
            token: this.token
          }
        });
      } catch (error) {
        console.error('Logout error:', error);
      } finally {
        this.clearAuth();
        this.isLoading = false;

        // Redirect to login page
        if (import.meta.client) {
          await navigateTo('/login');
        }
      }
    },

    async verifyToken(): Promise<boolean> {
      if (!this.token) return false;

      try {
        const response = await $fetch<{ user: User }>('/api/verify-token', {
          method: 'POST',
          body: { token: this.token }
        });

        if (response.user) {
          this.setUser(UserImpl.fromJSON(response.user));
          this.persistSession();
          return true;
        } else {
          this.clearAuth();
          return false;
        }
      } catch (error) {
        console.error('Token verification failed:', error);
        if (error && typeof error === 'object' && 'status' in error && error.status === 401) {
          this.clearAuth();
        }
        return false;
      }
    },

    async refreshAuthToken(): Promise<boolean> {
      if (!this.refreshToken) return false;

      try {
        const response = await $fetch<LoginResponse>('/api/refresh-token', {
          method: 'POST',
          body: { refreshToken: this.refreshToken }
        });

        if (response.success && response.token) {
          this.setTokens(response.token, response.refreshToken, response.expiresIn);
          this.persistSession();
          return true;
        } else {
          this.clearAuth();
          return false;
        }
      } catch (error) {
        console.error('Token refresh failed:', error);
        this.clearAuth();
        return false;
      }
    },

    async initializeAuth(): Promise<boolean> {
      this.isLoading = true;
      try {
        // First restore session synchronously
        const sessionRestored = this.restoreSession();

        // If session was restored, verify token
        if (sessionRestored && this.token) {
          const isValid = await this.verifyToken();
          if (isValid) {
            this.setupStorageListener();
            return true;
          }
        }

        // Set up cross-window/tab synchronization even if not authenticated
        if (import.meta.client) {
          this.setupStorageListener();
        }

        return sessionRestored;
      } finally {
        this.isLoading = false;
      }
    },

    setupStorageListener() {
      if (!import.meta.client) return;

      // Listen for storage changes in other windows/tabs
      window.addEventListener('storage', (e) => {
        if (e.key === 'auth-session') {
          if (e.newValue) {
            // Another tab logged in
            try {
              const sessionData: SessionData = JSON.parse(e.newValue);
              this.user = UserImpl.fromJSON(sessionData.user);
              this.token = sessionData.token;
              this.refreshToken = sessionData.refreshToken || null;
              this.isAuthenticated = sessionData.isAuthenticated;
              
              if (sessionData.sessionExpiry) {
                this.sessionExpiry = new Date(sessionData.sessionExpiry);
              }

              // Update sessionStorage for current tab
              sessionStorage.setItem('auth-session', e.newValue);
            } catch (error) {
              console.error('Error syncing auth state:', error);
            }
          } else {
            // Another tab logged out
            this.clearAuth();
          }
        } else if (e.key === 'auth-active') {
          if (!e.newValue) {
            // Auth was cleared in another tab
            this.clearAuth();
          }
        }
      });

      // Ensure session data is persisted before page unload
      window.addEventListener('beforeunload', () => {
        if (this.isAuthenticated) {
          this.persistSession();
        }
      });
    },

    syncAuthState() {
      if (!import.meta.client) return;

      const sessionData = localStorage.getItem('auth-session');
      if (sessionData) {
        try {
          const data: SessionData = JSON.parse(sessionData);
          this.user = UserImpl.fromJSON(data.user);
          this.token = data.token;
          this.refreshToken = data.refreshToken || null;
          this.isAuthenticated = data.isAuthenticated;
          
          if (data.sessionExpiry) {
            this.sessionExpiry = new Date(data.sessionExpiry);
          }

          // Update sessionStorage for current tab
          sessionStorage.setItem('auth-session', sessionData);
        } catch (error) {
          console.error('Error syncing auth state:', error);
        }
      }
    }
  }
});
