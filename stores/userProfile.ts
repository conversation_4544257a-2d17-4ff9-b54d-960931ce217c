import { defineStore } from 'pinia';
import type {
  User,
  UserProfile,
  UserPreferences,
  UserSubscription,
  SubscriptionPlan,
  UpdateUserProfileRequest,
  UpdateUserPreferencesRequest
} from '~/types/User';
import { useAuthStore } from './auth';

interface UserProfileState {
  isLoading: boolean;
  error: string | null;
  availablePlans: SubscriptionPlan[];
}

interface ApiResponse {
  success: boolean;
  message: string;
}

export const useUserProfileStore = defineStore('userProfile', {
  state: (): UserProfileState => ({
    isLoading: false,
    error: null,
    availablePlans: [
      {
        id: 'free',
        name: 'Free',
        description: 'Perfect for getting started',
        price: 0,
        currency: 'EUR',
        billingInterval: 'monthly',
        minutes: 60,
        features: [
          '60 minutes per month',
          'Basic support',
          'Standard quality',
          'Basic features'
        ],
        limits: {
          maxFolders: 5,
          maxFileSize: 50 * 1024 * 1024, // 50MB
          supportLevel: 'basic'
        }
      },
      {
        id: 'pro',
        name: 'Pro',
        description: 'For professionals and small teams',
        price: 19.99,
        originalPrice: 29.99,
        currency: 'EUR',
        billingInterval: 'monthly',
        minutes: 300,
        features: [
          '300 minutes per month',
          'Priority support',
          'High quality',
          'Advanced features',
          'Custom folders'
        ],
        limits: {
          maxFolders: 25,
          maxFileSize: 200 * 1024 * 1024, // 200MB
          supportLevel: 'basic'
        },
        isPopular: true
      },
      {
        id: 'premium',
        name: 'Premium',
        description: 'For power users and large teams',
        price: 39.99,
        originalPrice: 49.99,
        currency: 'EUR',
        billingInterval: 'monthly',
        minutes: 1000,
        features: [
          '1000 minutes per month',
          '24/7 support',
          'Premium quality',
          'All features',
          'Unlimited folders',
          'API access'
        ],
        limits: {
          maxFileSize: 500 * 1024 * 1024, // 500MB
          supportLevel: 'premium'
        },
        isRecommended: true
      }
    ]
  }),

  getters: {
    currentUser: (): User | null => {
      const authStore = useAuthStore();
      return authStore.user;
    },

    currentProfile: (): UserProfile | null => {
      const authStore = useAuthStore();
      return authStore.user?.profile || null;
    },

    currentPreferences: (): UserPreferences | null => {
      const authStore = useAuthStore();
      return authStore.user?.preferences || null;
    },

    currentSubscription: (): UserSubscription | null => {
      const authStore = useAuthStore();
      return authStore.user?.subscription || null;
    },

    getCurrentPlan: (state) => (): SubscriptionPlan | null => {
      const authStore = useAuthStore();
      if (!authStore.user) return null;

      return (
        state.availablePlans.find(
          (plan: SubscriptionPlan) =>
            plan.id === authStore.user?.subscription.planId
        ) || state.availablePlans[0]
      ); // Default to free plan
    },

    getAvailablePlans: (state) => (): SubscriptionPlan[] => {
      return state.availablePlans;
    },

    canUpgrade: () => (): boolean => {
      const authStore = useAuthStore();
      const currentPlan = authStore.user?.subscription.planId;
      return currentPlan === 'free' || currentPlan === 'pro';
    },

    getRemainingMinutes: () => (): number => {
      const authStore = useAuthStore();
      return authStore.user?.subscription.minutesRemaining || 0;
    },

    getUsagePercentage: () => (): number => {
      const authStore = useAuthStore();
      const subscription = authStore.user?.subscription;
      if (!subscription) return 0;

      const used = subscription.minutesUsed;
      const total = subscription.totalMinutes;
      return total > 0 ? Math.round((used / total) * 100) : 0;
    }
  },

  actions: {
    setError(error: string | null) {
      this.error = error;
    },

    clearError() {
      this.error = null;
    },

    async updateProfile(
      profileData: Partial<UserProfile>
    ): Promise<ApiResponse> {
      this.isLoading = true;
      this.clearError();

      try {
        const authStore = useAuthStore();
        if (!authStore.user || !authStore.token) {
          throw new Error('User not authenticated');
        }

        const response = await $fetch<ApiResponse>('/api/user/profile', {
          method: 'PUT',
          headers: {
            Authorization: `Bearer ${authStore.token}`
          },
          body: { profile: profileData } as UpdateUserProfileRequest
        });

        if (response.success) {
          // Update the user in auth store
          const updatedUser = {
            ...authStore.user,
            profile: { ...authStore.user.profile, ...profileData }
          };
          authStore.setUser(updatedUser);
          authStore.persistSession();
        }

        return response;
      } catch (error) {
        console.error('Error updating profile:', error);
        const errorMessage = 'Failed to update profile';
        this.setError(errorMessage);
        return { success: false, message: errorMessage };
      } finally {
        this.isLoading = false;
      }
    },

    async updatePreferences(
      preferencesData: Partial<UserPreferences>
    ): Promise<ApiResponse> {
      this.isLoading = true;
      this.clearError();

      try {
        const authStore = useAuthStore();
        if (!authStore.user || !authStore.token) {
          throw new Error('User not authenticated');
        }

        const response = await $fetch<ApiResponse>('/api/user/preferences', {
          method: 'PUT',
          headers: {
            Authorization: `Bearer ${authStore.token}`
          },
          body: { preferences: preferencesData } as UpdateUserPreferencesRequest
        });

        if (response.success) {
          // Update the user in auth store
          const updatedUser = {
            ...authStore.user,
            preferences: { ...authStore.user.preferences, ...preferencesData }
          };
          authStore.setUser(updatedUser);
          authStore.persistSession();
        }

        return response;
      } catch (error) {
        console.error('Error updating preferences:', error);
        const errorMessage = 'Failed to update preferences';
        this.setError(errorMessage);
        return { success: false, message: errorMessage };
      } finally {
        this.isLoading = false;
      }
    },

    async updateEmail(email: string): Promise<ApiResponse> {
      this.isLoading = true;
      this.clearError();

      try {
        const authStore = useAuthStore();
        if (!authStore.user || !authStore.token) {
          throw new Error('User not authenticated');
        }

        const response = await $fetch<ApiResponse>('/api/user/email', {
          method: 'PUT',
          headers: {
            Authorization: `Bearer ${authStore.token}`
          },
          body: { email }
        });

        if (response.success) {
          // Update the user in auth store
          const updatedUser = {
            ...authStore.user,
            email
          };
          authStore.setUser(updatedUser);
          authStore.persistSession();
        }

        return response;
      } catch (error) {
        console.error('Error updating email:', error);
        const errorMessage = 'Failed to update email';
        this.setError(errorMessage);
        return { success: false, message: errorMessage };
      } finally {
        this.isLoading = false;
      }
    },

    async updatePassword(
      oldPassword: string,
      newPassword: string
    ): Promise<ApiResponse> {
      this.isLoading = true;
      this.clearError();

      try {
        const authStore = useAuthStore();
        if (!authStore.user || !authStore.token) {
          throw new Error('User not authenticated');
        }

        const response = await $fetch<ApiResponse>('/api/user/password', {
          method: 'PUT',
          headers: {
            Authorization: `Bearer ${authStore.token}`
          },
          body: { oldPassword, newPassword }
        });

        return response;
      } catch (error) {
        console.error('Error updating password:', error);
        const errorMessage = 'Failed to update password';
        this.setError(errorMessage);
        return { success: false, message: errorMessage };
      } finally {
        this.isLoading = false;
      }
    },

    async updateSubscription(planId: string): Promise<ApiResponse> {
      this.isLoading = true;
      this.clearError();

      try {
        const authStore = useAuthStore();
        if (!authStore.user || !authStore.token) {
          throw new Error('User not authenticated');
        }

        const plan = this.availablePlans.find((p) => p.id === planId);
        if (!plan) {
          throw new Error('Invalid plan selected');
        }

        const response = await $fetch<ApiResponse>('/api/user/subscription', {
          method: 'PUT',
          headers: {
            Authorization: `Bearer ${authStore.token}`
          },
          body: { planId }
        });

        if (response.success) {
          // Update the user subscription in auth store
          const updatedSubscription: UserSubscription = {
            ...authStore.user.subscription,
            planId: plan.id,
            planName: plan.name,
            totalMinutes: plan.minutes,
            minutesRemaining: plan.minutes,
            status: 'active',
            currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
          };

          const updatedUser = {
            ...authStore.user,
            subscription: updatedSubscription
          };

          authStore.setUser(updatedUser);
          authStore.persistSession();
        }

        return response;
      } catch (error) {
        console.error('Error updating subscription:', error);
        const errorMessage = 'Failed to update subscription';
        this.setError(errorMessage);
        return { success: false, message: errorMessage };
      } finally {
        this.isLoading = false;
      }
    },

    async deleteAccount(): Promise<ApiResponse> {
      this.isLoading = true;
      this.clearError();

      try {
        const authStore = useAuthStore();
        if (!authStore.user || !authStore.token) {
          throw new Error('User not authenticated');
        }

        const response = await $fetch<ApiResponse>('/api/user/delete', {
          method: 'DELETE',
          headers: {
            Authorization: `Bearer ${authStore.token}`
          }
        });

        if (response.success) {
          // Clear auth and redirect
          authStore.clearAuth();
          if (import.meta.client) {
            await navigateTo('/login');
          }
        }

        return response;
      } catch (error) {
        console.error('Error deleting account:', error);
        const errorMessage = 'Failed to delete account';
        this.setError(errorMessage);
        return { success: false, message: errorMessage };
      } finally {
        this.isLoading = false;
      }
    }
  }
});
