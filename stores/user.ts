import { defineStore } from 'pinia';
import type {
  User,
  UserProfile,
  SubscriptionPlan,
  UserSubscription,
  LoginCredentials,
  LoginResponse
} from '~/types/User';
import { Note } from '~/types/Note';
import type { NoteData, NoteStatus } from '~/types/Note';
import { useAuthStore } from './auth';

export const useUserStore = defineStore('user', {
  state: () => ({
    // Authentication state is now delegated to auth store
    isLoading: false,
    // Notes state
    notes: [] as Note[],
    notesLoading: false,
    notesError: null as string | null,
    folders: [] as string[],
    foldersLoading: false,
    foldersError: null as string | null,
    availablePlans: [
      {
        id: 'free',
        name: 'Free',
        price: 0,
        currency: 'EUR',
        minutes: 60,
        features: [
          '60 minutes per month',
          'Basic support',
          'Standard quality',
          'Basic features'
        ]
      },
      {
        id: 'pro',
        name: 'Pro',
        price: 19.99,
        originalPrice: 29.99,
        currency: 'EUR',
        minutes: 300,
        features: [
          '300 minutes per month',
          'Priority support',
          'High quality',
          'Advanced features'
        ],
        isPopular: true
      },
      {
        id: 'premium',
        name: 'Premium',
        price: 39.99,
        originalPrice: 49.99,
        currency: 'EUR',
        minutes: 1000,
        features: [
          '1000 minutes per month',
          '24/7 support',
          'Premium quality',
          'All features'
        ]
      }
    ] as SubscriptionPlan[]
  }),
  getters: {
    // Delegate authentication to auth store
    user: () => {
      const authStore = useAuthStore();
      return authStore.user;
    },

    isAuthenticated: () => {
      const authStore = useAuthStore();
      return authStore.isAuthenticated;
    },

    token: () => {
      const authStore = useAuthStore();
      return authStore.token;
    },

    isLoggedIn: () => {
      const authStore = useAuthStore();
      return authStore.isLoggedIn;
    },

    // Notes getters
    userNotes: (state) => {
      const authStore = useAuthStore();
      if (!authStore.user) return [];
      return state.notes.filter((note) => note.userId === authStore.user!.id);
    },

    userFolders: (state) => {
      const authStore = useAuthStore();
      if (!authStore.user) return [];
      return state.folders;
    },

    notesByStatus: (state) => (status: NoteStatus) => {
      const authStore = useAuthStore();
      if (!authStore.user) return [];
      return state.notes.filter(
        (note) => note.userId === authStore.user!.id && note.status === status
      );
    },

    notesByFolder: (state) => (folder: string | null) => {
      const authStore = useAuthStore();
      if (!authStore.user) return [];
      return state.notes.filter(
        (note) => note.userId === authStore.user!.id && note.folder === folder
      );
    },

    getNoteById: (state) => (id: number) => {
      return state.notes.find((note) => note.id === id);
    },

    processingNotesCount: (state) => {
      const authStore = useAuthStore();
      if (!authStore.user) return 0;
      return state.notes.filter(
        (note) =>
          authStore.user &&
          note.userId === authStore.user.id &&
          note.status === 'processing'
      ).length;
    }
  },
  actions: {
    // Session management methods - delegate to auth store
    setRedirectPath(path: string) {
      const authStore = useAuthStore();
      authStore.setRedirectPath(path);
    },

    getRedirectPath(): string {
      const authStore = useAuthStore();
      return authStore.getRedirectPath();
    },

    clearRedirectPath() {
      const authStore = useAuthStore();
      authStore.clearRedirectPath();
    },
    // Session persistence is now handled by auth store

    // Session restoration is now handled by auth store
    restoreSession(): boolean {
      const authStore = useAuthStore();
      return authStore.restoreSession();
    },

    // Session clearing is now handled by auth store
    clearSession() {
      const authStore = useAuthStore();
      authStore.clearSession();
    },
    setUser(userData: User) {
      const authStore = useAuthStore();
      authStore.setUser(userData);
    },

    setToken(token: string) {
      const authStore = useAuthStore();
      authStore.setTokens(token);
    },

    clearAuth() {
      const authStore = useAuthStore();
      authStore.clearAuth();

      // Clear notes when logging out
      this.clearNotes();
    },

    async login(credentials: LoginCredentials): Promise<LoginResponse> {
      const authStore = useAuthStore();
      this.isLoading = true;

      try {
        const response = await authStore.login(credentials);

        if (response.success) {
          // Load user notes after successful login
          this.loadUserNotes().catch((error) => {
            console.error('Failed to load notes after login:', error);
          });
        }

        return response;
      } finally {
        this.isLoading = false;
      }
    },

    async logout() {
      const authStore = useAuthStore();
      this.clearNotes(); // Clear notes before logout
      await authStore.logout();
    },

    // Auth checking is now handled by auth store
    async checkAuth() {
      const authStore = useAuthStore();
      return await authStore.verifyToken();
    },

    async initializeAuth() {
      const authStore = useAuthStore();
      const result = await authStore.initializeAuth();

      // Load user notes if authenticated
      if (result && authStore.isAuthenticated) {
        this.loadUserNotes().catch((error) => {
          console.error(
            'Failed to load notes after auth initialization:',
            error
          );
        });
      }

      return result;
    },

    async verifyTokenInBackground() {
      if (!this.token) return;

      try {
        const response = await $fetch<{ user: User }>('/api/verify-token', {
          method: 'POST',
          body: { token: this.token }
        });

        if (response.user) {
          // Update user data and refresh session
          this.setUser(response.user);
        } else {
          // Token is invalid, clear auth
          this.clearAuth();
        }
      } catch (error) {
        console.error('Background token verification failed:', error);
        // Only clear auth if it's a 401 error (unauthorized)
        if (
          error &&
          typeof error === 'object' &&
          'status' in error &&
          error.status === 401
        ) {
          this.clearAuth();
        }
      }
    },
    // Auth state sync is now handled by auth store
    syncAuthState() {
      const authStore = useAuthStore();
      authStore.syncAuthState();
    },

    async updateUserProfile(profile: UserProfile) {
      this.isLoading = true;
      try {
        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 1000));

        if (this.user) {
          this.user.profile = profile;
        }

        return {
          success: true,
          message: 'Profile updated successfully'
        };
      } catch (error) {
        console.error('Error updating profile:', error);
        return { success: false, message: 'Failed to update profile' };
      } finally {
        this.isLoading = false;
      }
    },

    async updateEmail(email: string) {
      this.isLoading = true;
      try {
        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 1000));

        if (this.user) {
          this.user.email = email;
        }

        return {
          success: true,
          message: 'Email updated successfully'
        };
      } catch (error) {
        console.error('Error updating email:', error);
        return { success: false, message: 'Failed to update email' };
      } finally {
        this.isLoading = false;
      }
    },

    async updatePassword(oldPassword: string, newPassword: string) {
      const { updatePassword } = useUserProfile();
      return await updatePassword(oldPassword, newPassword);
    },

    async updateSubscriptionPlan(planId: string) {
      this.isLoading = true;
      try {
        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 1000));

        const plan = this.availablePlans.find((p) => p.id === planId);
        if (plan && this.user) {
          this.user.subscription = {
            planId: plan.id,
            planName: plan.name,
            status: 'active',
            currentPeriodStart: new Date(),
            currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
            totalMinutes: plan.minutes,
            minutesUsed: 0,
            minutesRemaining: plan.minutes,
            autoRenew: true
          } as UserSubscription;
        }

        return {
          success: true,
          message: 'Subscription updated successfully'
        };
      } catch (error) {
        console.error('Error updating subscription:', error);
        return {
          success: false,
          message: 'Failed to update subscription'
        };
      } finally {
        this.isLoading = false;
      }
    },

    getCurrentPlan() {
      if (!this.user) return null;
      return (
        this.availablePlans.find(
          (plan: SubscriptionPlan) =>
            plan.name.toLowerCase() ===
            this.user?.subscriptionType.toLowerCase()
        ) || this.availablePlans[0]
      );
    },

    // Notes actions
    async loadUserNotes() {
      if (!this.isAuthenticated || !this.user || !this.token) {
        this.notesError = 'User not authenticated';
        return;
      }

      this.notesLoading = true;
      this.notesError = null;

      try {
        const responseNotes = await $fetch<{ notes: NoteData[] }>(
          '/api/notes',
          {
            method: 'GET',
            headers: {
              Authorization: `Bearer ${this.token}`
            }
          }
        );

        this.notes = responseNotes.notes
          .map((noteData) => Note.fromJSON(noteData))
          .sort((note1, note2) => note2.date.getTime() - note1.date.getTime());

        const responseFolders = await $fetch<{ folders: string[] }>(
          '/api/folders',
          {
            method: 'GET',
            headers: {
              Authorization: `Bearer ${this.token}`
            }
          }
        );
        this.folders = responseFolders.folders;
      } catch (error: unknown) {
        console.error('Failed to load notes:', error);

        if (
          error &&
          typeof error === 'object' &&
          'status' in error &&
          error.status === 401
        ) {
          this.notesError = 'Authentication required. Please log in again.';
          this.clearAuth();
        } else {
          this.notesError = 'Failed to load notes';
        }
      } finally {
        this.notesLoading = false;
      }
    },

    async createNote(noteData: {
      name: string;
      duration: string;
      folder: string | null;
      fileId?: string;
      transcriptionId?: string;
      fileUrl?: string;
      fileSize?: number;
      fileType?: string;
    }): Promise<Note | null> {
      if (!this.isAuthenticated || !this.user) {
        this.notesError = 'User not authenticated';
        return null;
      }

      this.notesLoading = true;
      this.notesError = null;

      try {
        const newNote = new Note(
          Date.now(),
          noteData.name,
          new Date(),
          noteData.duration,
          noteData.folder,
          'processing',
          this.user.id,
          {
            fileId: noteData.fileId,
            transcriptionId: noteData.transcriptionId,
            fileUrl: noteData.fileUrl,
            fileSize: noteData.fileSize,
            fileType: noteData.fileType
          }
        );

        this.notes.push(newNote);
        return newNote;
      } catch (error) {
        console.error('Failed to create note:', error);
        this.notesError = 'Failed to create note';
        return null;
      } finally {
        this.notesLoading = false;
      }
    },

    updateNoteStatus(noteId: number, status: NoteStatus) {
      const note = this.notes.find((n) => n.id === noteId);
      if (note) {
        note.updateStatus(status);
      }
    },

    updateNote(noteId: number, updates: Partial<NoteData>) {
      const noteIndex = this.notes.findIndex((n) => n.id === noteId);
      if (noteIndex !== -1) {
        const currentNote = this.notes[noteIndex];
        const updatedNoteData = { ...currentNote.toJSON(), ...updates };
        this.notes[noteIndex] = Note.fromJSON(updatedNoteData);
      }
    },

    async deleteNote(noteId: number): Promise<boolean> {
      if (!this.isAuthenticated || !this.user || !this.token) {
        this.notesError = 'User not authenticated';
        return false;
      }

      this.notesLoading = true;
      this.notesError = null;

      try {
        await $fetch(`/api/notes/${noteId}`, {
          method: 'DELETE',
          headers: {
            Authorization: `Bearer ${this.token}`
          }
        });

        this.notes = this.notes.filter((note) => note.id !== noteId);
        return true;
      } catch (error) {
        console.error('Failed to delete note:', error);
        this.notesError = 'Failed to delete note';
        return false;
      } finally {
        this.notesLoading = false;
      }
    },

    async downloadNote(
      noteId: number,
      format: 'pdf' | 'docx'
    ): Promise<boolean> {
      if (!this.isAuthenticated || !this.user || !this.token) {
        this.notesError = 'User not authenticated';
        return false;
      }

      try {
        const response = await fetch(
          `/api/notes/${noteId}/download/${format}`,
          {
            method: 'GET',
            headers: {
              Authorization: `Bearer ${this.token}`
            }
          }
        );

        if (!response.ok) throw new Error('Failed to download');

        const blob = await response.blob();
        const disposition = response.headers.get('Content-Disposition');
        const match = disposition?.match(/filename="?(.+?)"?$/);
        const filename = match?.[1] || `note-${noteId}.${format}`;

        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.click();
        window.URL.revokeObjectURL(url);

        return true;
      } catch (error) {
        console.error('Failed to download note:', error);
        this.notesError = 'Failed to download note';
        return false;
      }
    },

    clearNotesError() {
      this.notesError = null;
    },

    clearNotes() {
      this.notes = [];
      this.notesError = null;
      this.notesLoading = false;
    },

    async createFolder(folder: string): Promise<string | null> {
      if (!this.isAuthenticated || !this.user) {
        this.notesError = 'User not authenticated';
        return null;
      }

      try {
        this.folders.push(folder);
        return folder;
      } catch (error) {
        console.error('Failed to create folder:', error);
        this.notesError = 'Failed to create folder';
        return null;
      } finally {
        this.notesLoading = false;
      }
    }
  }
});
