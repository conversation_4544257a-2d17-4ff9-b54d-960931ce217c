import { defineStore } from 'pinia';
import type {
  User,
  UserProfile,
  SubscriptionPlan,
  UserSubscription
} from '~/types/User';
import { Note } from '~/types/Note';
import type { NoteData, NoteStatus } from '~/types/Note';

interface LoginCredentials {
  username: string;
  password: string;
}

interface LoginResponse {
  success: boolean;
  user?: User;
  token?: string;
  message?: string;
}

export const useUserStore = defineStore('user', {
  state: () => ({
    user: null as null | User,
    isAuthenticated: false,
    token: null as null | string,
    isLoading: false,
    // Notes state
    notes: [] as Note[],
    notesLoading: false,
    notesError: null as string | null,
    folders: [] as string[],
    foldersLoading: false,
    foldersError: null as string | null,
    availablePlans: [
      {
        id: 'free',
        name: 'Free',
        price: 0,
        currency: 'EUR',
        minutes: 60,
        features: [
          '60 minutes per month',
          'Basic support',
          'Standard quality',
          'Basic features'
        ]
      },
      {
        id: 'pro',
        name: 'Pro',
        price: 19.99,
        originalPrice: 29.99,
        currency: 'EUR',
        minutes: 300,
        features: [
          '300 minutes per month',
          'Priority support',
          'High quality',
          'Advanced features'
        ],
        isPopular: true
      },
      {
        id: 'premium',
        name: 'Premium',
        price: 39.99,
        originalPrice: 49.99,
        currency: 'EUR',
        minutes: 1000,
        features: [
          '1000 minutes per month',
          '24/7 support',
          'Premium quality',
          'All features'
        ]
      }
    ] as SubscriptionPlan[]
  }),
  getters: {
    isLoggedIn: (state) => state.isAuthenticated && state.user !== null,

    // Notes getters
    userNotes: (state) => {
      if (!state.user) return [];
      return state.notes.filter((note) => note.userId === state.user!.id);
    },
    // Notes getters
    userFolders: (state) => {
      if (!state.user) return [];
      return state.folders;
    },

    notesByStatus: (state) => (status: NoteStatus) => {
      if (!state.user) return [];
      return state.notes.filter(
        (note) => note.userId === state.user!.id && note.status === status
      );
    },

    notesByFolder: (state) => (folder: string | null) => {
      if (!state.user) return [];
      return state.notes.filter(
        (note) => note.userId === state.user!.id && note.folder === folder
      );
    },

    getNoteById: (state) => (id: number) => {
      return state.notes.find((note) => note.id === id);
    },

    processingNotesCount: (state) => {
      if (!state.user) return 0;
      return state.notes.filter(
        (note) =>
          state.user &&
          note.userId === state.user.id &&
          note.status === 'processing'
      ).length;
    }
  },
  actions: {
    setRedirectPath(path: string) {
      if (import.meta.client) {
        sessionStorage.setItem('redirectAfterLogin', path);
      }
    },

    getRedirectPath(): string {
      if (import.meta.client) {
        return sessionStorage.getItem('redirectAfterLogin') || '/dashboard';
      }
      return '/dashboard';
    },

    clearRedirectPath() {
      if (import.meta.client) {
        sessionStorage.removeItem('redirectAfterLogin');
      }
    },
    persistSession() {
      if (import.meta.client && this.user && this.token) {
        // Store session data in both localStorage and sessionStorage
        const sessionData = {
          user: this.user,
          token: this.token,
          timestamp: Date.now(),
          isAuthenticated: true
        };

        const sessionDataString = JSON.stringify(sessionData);

        // localStorage for persistence across browser sessions
        localStorage.setItem('auth-session', sessionDataString);

        // sessionStorage for current browser session
        sessionStorage.setItem('auth-session', sessionDataString);

        // Set a flag to indicate active session
        localStorage.setItem('auth-active', 'true');

        // Also store in cookies as additional fallback
        const { setCookie } = useCookies();
        setCookie('auth-session', sessionDataString, 7);
        setCookie('auth-token', this.token, 7);
      }
    },

    restoreSession(): boolean {
      if (!import.meta.client) return false;

      try {
        // First try sessionStorage (current session)
        let sessionData = sessionStorage.getItem('auth-session');

        // If not in sessionStorage, try localStorage (persistent)
        if (!sessionData) {
          sessionData = localStorage.getItem('auth-session');

          // If found in localStorage, also set in sessionStorage for current session
          if (sessionData) {
            sessionStorage.setItem('auth-session', sessionData);
          }
        }

        // If still no data, try cookies as final fallback
        if (!sessionData) {
          const { getCookie } = useCookies();
          sessionData = getCookie('auth-session');

          // If found in cookies, restore to storage
          if (sessionData) {
            localStorage.setItem('auth-session', sessionData);
            sessionStorage.setItem('auth-session', sessionData);
          }
        }

        if (sessionData) {
          const data = JSON.parse(sessionData);

          // Check if session is not too old (optional: 7 days)
          const maxAge = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds
          if (Date.now() - data.timestamp > maxAge) {
            this.clearAuth();
            return false;
          }

          // Restore user state immediately
          this.user = data.user;
          this.token = data.token;
          this.isAuthenticated = data.isAuthenticated;

          return true;
        }
      } catch (error) {
        console.error('Error restoring session:', error);
        this.clearAuth();
      }
      return false;
    },

    clearSession() {
      if (import.meta.client) {
        localStorage.removeItem('auth-session');
        localStorage.removeItem('auth-active');
        sessionStorage.removeItem('auth-session');

        // Also clear cookies
        const { deleteCookie } = useCookies();
        deleteCookie('auth-session');
        deleteCookie('auth-token');
      }
    },
    setUser(userData: User) {
      this.user = userData;
      this.isAuthenticated = true;
    },

    setToken(token: string) {
      this.token = token;
      // Store token in localStorage for persistence
      if (import.meta.client) {
        localStorage.setItem('auth-token', token);
      }
    },

    clearAuth() {
      this.user = null;
      this.isAuthenticated = false;
      this.token = null;
      this.clearSession();
      if (import.meta.client) {
        localStorage.removeItem('auth-token');
      }

      // Clear notes when logging out
      this.clearNotes();
    },

    async login(credentials: LoginCredentials): Promise<LoginResponse> {
      this.isLoading = true;
      try {
        const response = await $fetch<LoginResponse>('/api/login', {
          method: 'POST',
          body: credentials
        });

        if (response.success && response.user && response.token) {
          this.setUser(response.user);
          this.setToken(response.token);
          this.persistSession();

          // Load user notes after successful login
          this.loadUserNotes().catch((error) => {
            console.error('Failed to load notes after login:', error);
          });

          return response;
        } else {
          this.clearAuth();
          return response;
        }
      } catch (error) {
        console.error('Login error:', error);
        this.clearAuth();
        return {
          success: false,
          message: 'Login failed. Please try again.'
        };
      } finally {
        this.isLoading = false;
      }
    },

    async logout() {
      this.isLoading = true;
      try {
        await $fetch('/api/logout', {
          method: 'POST',
          body: {
            id: this.user?.id,
            token: this.token
          }
        });
      } catch (error) {
        console.error('Logout error:', error);
      } finally {
        this.clearAuth();
        this.isLoading = false;

        // Redirect to login page
        if (import.meta.client) {
          await navigateTo('/login');
        }
      }
    },

    async checkAuth() {
      if (!import.meta.client) return false;

      // First try to restore from session storage
      if (this.restoreSession()) {
        // If we have a token, verify it's still valid
        if (this.token) {
          try {
            const response = await $fetch<{ user: User }>('/api/verify-token', {
              method: 'POST',
              body: { token: this.token }
            });

            if (response.user) {
              // Update user data in case it changed
              this.setUser(response.user);
              this.persistSession(); // Refresh session timestamp
              return true;
            }
          } catch (error) {
            console.error('Token verification failed:', error);
            this.clearAuth();
            return false;
          }
        }
        // If no token but session was restored, something is wrong
        this.clearAuth();
        return false;
      }

      // Fallback to old token method for backward compatibility
      const token = localStorage.getItem('auth-token');
      if (token) {
        this.token = token;
        try {
          const response = await $fetch<{ user: User }>('/api/verify-token', {
            method: 'POST',
            body: { token }
          });

          if (response.user) {
            this.setUser(response.user);
            this.persistSession(); // Create new session
            return true;
          }
        } catch (error) {
          console.error('Token verification failed:', error);
          this.clearAuth();
        }
      }

      return false;
    },

    async initializeAuth() {
      this.isLoading = true;
      try {
        // First restore session synchronously
        const sessionRestored = this.restoreSession();

        // If session was restored, verify token immediately to ensure it's valid
        if (sessionRestored && this.token) {
          try {
            const response = await $fetch<{ user: User }>('/api/verify-token', {
              method: 'POST',
              body: { token: this.token }
            });

            if (response.user) {
              // Update user data in case it changed
              this.setUser(response.user);
              this.persistSession(); // Refresh session timestamp

              // Load user notes after successful auth restoration
              this.loadUserNotes().catch((error) => {
                console.error(
                  'Failed to load notes after auth restoration:',
                  error
                );
              });

              // Set up cross-window/tab synchronization
              if (import.meta.client) {
                this.setupStorageListener();
              }

              return true;
            } else {
              // Token is invalid, clear auth
              this.clearAuth();
              return false;
            }
          } catch (error) {
            console.error(
              'Token verification failed during initialization:',
              error
            );
            this.clearAuth();
            return false;
          }
        }

        // Set up cross-window/tab synchronization even if not authenticated
        if (import.meta.client) {
          this.setupStorageListener();
        }

        return sessionRestored;
      } finally {
        this.isLoading = false;
      }
    },

    async verifyTokenInBackground() {
      if (!this.token) return;

      try {
        const response = await $fetch<{ user: User }>('/api/verify-token', {
          method: 'POST',
          body: { token: this.token }
        });

        if (response.user) {
          // Update user data and refresh session
          this.setUser(response.user);
          this.persistSession();
        } else {
          // Token is invalid, clear auth
          this.clearAuth();
        }
      } catch (error) {
        console.error('Background token verification failed:', error);
        // Only clear auth if it's a 401 error (unauthorized)
        if (
          error &&
          typeof error === 'object' &&
          'status' in error &&
          error.status === 401
        ) {
          this.clearAuth();
        }
      }
    },

    setupStorageListener() {
      if (!import.meta.client) return;

      // Listen for storage changes in other windows/tabs
      window.addEventListener('storage', (e) => {
        if (e.key === 'auth-session') {
          if (e.newValue) {
            // Another tab logged in
            try {
              const sessionData = JSON.parse(e.newValue);
              this.user = sessionData.user;
              this.token = sessionData.token;
              this.isAuthenticated = sessionData.isAuthenticated;

              // Also update sessionStorage for current tab
              sessionStorage.setItem('auth-session', e.newValue);
            } catch (error) {
              console.error('Error syncing auth state:', error);
            }
          } else {
            // Another tab logged out
            this.clearAuth();
          }
        } else if (e.key === 'auth-active') {
          if (!e.newValue) {
            // Auth was cleared in another tab
            this.clearAuth();
          }
        }
      });

      // Listen for session storage changes (same tab, different page)
      window.addEventListener('beforeunload', () => {
        // Ensure session data is persisted before page unload
        if (this.isAuthenticated) {
          this.persistSession();
        }
      });
    },

    // Method to manually sync auth state across tabs
    syncAuthState() {
      if (!import.meta.client) return;

      const sessionData = localStorage.getItem('auth-session');
      if (sessionData) {
        try {
          const data = JSON.parse(sessionData);
          this.user = data.user;
          this.token = data.token;
          this.isAuthenticated = data.isAuthenticated;

          // Update sessionStorage for current tab
          sessionStorage.setItem('auth-session', sessionData);

          if (this.token && this.user && this.isAuthenticated) {
            this.loadUserNotes().catch((err) => {
              console.error('Failed to reload notes in syncAuthState:', err);
            });
          }
        } catch (error) {
          console.error('Error syncing auth state:', error);
        }
      }
    },

    async updateUserProfile(profile: UserProfile) {
      this.isLoading = true;
      try {
        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 1000));

        if (this.user) {
          this.user.profile = profile;
        }

        return {
          success: true,
          message: 'Profile updated successfully'
        };
      } catch (error) {
        console.error('Error updating profile:', error);
        return { success: false, message: 'Failed to update profile' };
      } finally {
        this.isLoading = false;
      }
    },

    async updateEmail(email: string) {
      this.isLoading = true;
      try {
        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 1000));

        if (this.user) {
          this.user.email = email;
        }

        return {
          success: true,
          message: 'Email updated successfully'
        };
      } catch (error) {
        console.error('Error updating email:', error);
        return { success: false, message: 'Failed to update email' };
      } finally {
        this.isLoading = false;
      }
    },

    async updatePassword(oldPassword: string, newPassword: string) {
      this.isLoading = true;
      oldPassword = newPassword;
      try {
        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 1000));

        return {
          success: true,
          message: 'Password updated successfully'
        };
      } catch (error) {
        console.error('Error updating password:', error);
        return { success: false, message: 'Failed to update password' };
      } finally {
        this.isLoading = false;
      }
    },

    async updateSubscriptionPlan(planId: string) {
      this.isLoading = true;
      try {
        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 1000));

        const plan = this.availablePlans.find((p) => p.id === planId);
        if (plan && this.user) {
          this.user.subscription = {
            planId: plan.id,
            planName: plan.name,
            status: 'active',
            currentPeriodStart: new Date(),
            currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
            totalMinutes: plan.minutes,
            minutesUsed: 0,
            minutesRemaining: plan.minutes,
            autoRenew: true
          } as UserSubscription;
        }

        return {
          success: true,
          message: 'Subscription updated successfully'
        };
      } catch (error) {
        console.error('Error updating subscription:', error);
        return {
          success: false,
          message: 'Failed to update subscription'
        };
      } finally {
        this.isLoading = false;
      }
    },

    getCurrentPlan() {
      if (!this.user) return null;
      return (
        this.availablePlans.find(
          (plan: SubscriptionPlan) =>
            plan.name.toLowerCase() ===
            this.user?.subscriptionType.toLowerCase()
        ) || this.availablePlans[0]
      );
    },

    // Notes actions
    async loadUserNotes() {
      if (!this.isAuthenticated || !this.user || !this.token) {
        this.notesError = 'User not authenticated';
        return;
      }

      this.notesLoading = true;
      this.notesError = null;

      try {
        const responseNotes = await $fetch<{ notes: NoteData[] }>(
          '/api/notes',
          {
            method: 'GET',
            headers: {
              Authorization: `Bearer ${this.token}`
            }
          }
        );

        this.notes = responseNotes.notes
          .map((noteData) => Note.fromJSON(noteData))
          .sort((note1, note2) => note2.date.getTime() - note1.date.getTime());

        const responseFolders = await $fetch<{ folders: string[] }>(
          '/api/folders',
          {
            method: 'GET',
            headers: {
              Authorization: `Bearer ${this.token}`
            }
          }
        );
        this.folders = responseFolders.folders;
      } catch (error: unknown) {
        console.error('Failed to load notes:', error);

        if (
          error &&
          typeof error === 'object' &&
          'status' in error &&
          error.status === 401
        ) {
          this.notesError = 'Authentication required. Please log in again.';
          this.clearAuth();
        } else {
          this.notesError = 'Failed to load notes';
        }
      } finally {
        this.notesLoading = false;
      }
    },

    async createNote(noteData: {
      name: string;
      duration: string;
      folder: string | null;
      fileId?: string;
      transcriptionId?: string;
      fileUrl?: string;
      fileSize?: number;
      fileType?: string;
    }): Promise<Note | null> {
      if (!this.isAuthenticated || !this.user) {
        this.notesError = 'User not authenticated';
        return null;
      }

      this.notesLoading = true;
      this.notesError = null;

      try {
        const newNote = new Note(
          Date.now(),
          noteData.name,
          new Date(),
          noteData.duration,
          noteData.folder,
          'processing',
          this.user.id,
          {
            fileId: noteData.fileId,
            transcriptionId: noteData.transcriptionId,
            fileUrl: noteData.fileUrl,
            fileSize: noteData.fileSize,
            fileType: noteData.fileType
          }
        );

        this.notes.push(newNote);
        return newNote;
      } catch (error) {
        console.error('Failed to create note:', error);
        this.notesError = 'Failed to create note';
        return null;
      } finally {
        this.notesLoading = false;
      }
    },

    updateNoteStatus(noteId: number, status: NoteStatus) {
      const note = this.notes.find((n) => n.id === noteId);
      if (note) {
        note.updateStatus(status);
      }
    },

    updateNote(noteId: number, updates: Partial<NoteData>) {
      const noteIndex = this.notes.findIndex((n) => n.id === noteId);
      if (noteIndex !== -1) {
        const currentNote = this.notes[noteIndex];
        const updatedNoteData = { ...currentNote.toJSON(), ...updates };
        this.notes[noteIndex] = Note.fromJSON(updatedNoteData);
      }
    },

    async deleteNote(noteId: number): Promise<boolean> {
      if (!this.isAuthenticated || !this.user || !this.token) {
        this.notesError = 'User not authenticated';
        return false;
      }

      this.notesLoading = true;
      this.notesError = null;

      try {
        await $fetch(`/api/notes/${noteId}`, {
          method: 'DELETE',
          headers: {
            Authorization: `Bearer ${this.token}`
          }
        });

        this.notes = this.notes.filter((note) => note.id !== noteId);
        return true;
      } catch (error) {
        console.error('Failed to delete note:', error);
        this.notesError = 'Failed to delete note';
        return false;
      } finally {
        this.notesLoading = false;
      }
    },

    async downloadNote(
      noteId: number,
      format: 'pdf' | 'docx'
    ): Promise<boolean> {
      if (!this.isAuthenticated || !this.user || !this.token) {
        this.notesError = 'User not authenticated';
        return false;
      }

      try {
        const response = await fetch(
          `/api/notes/${noteId}/download/${format}`,
          {
            method: 'GET',
            headers: {
              Authorization: `Bearer ${this.token}`
            }
          }
        );

        if (!response.ok) throw new Error('Failed to download');

        const blob = await response.blob();
        const disposition = response.headers.get('Content-Disposition');
        const match = disposition?.match(/filename="?(.+?)"?$/);
        const filename = match?.[1] || `note-${noteId}.${format}`;

        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.click();
        window.URL.revokeObjectURL(url);

        return true;
      } catch (error) {
        console.error('Failed to download note:', error);
        this.notesError = 'Failed to download note';
        return false;
      }
    },

    clearNotesError() {
      this.notesError = null;
    },

    clearNotes() {
      this.notes = [];
      this.notesError = null;
      this.notesLoading = false;
    },

    async createFolder(folder: string): Promise<string | null> {
      if (!this.isAuthenticated || !this.user) {
        this.notesError = 'User not authenticated';
        return null;
      }

      try {
        this.folders.push(folder);
        return folder;
      } catch (error) {
        console.error('Failed to create folder:', error);
        this.notesError = 'Failed to create folder';
        return null;
      } finally {
        this.notesLoading = false;
      }
    }
  }
});
