import { defineStore } from 'pinia';

interface TranscriptionState {
  isProcessing: boolean;
  currentFileId: string | null;
  progress: number;
  estimatedTime: number | null;
}

export const useTranscriptionStore = defineStore('transcription', {
  state: (): TranscriptionState => ({
    isProcessing: false,
    currentFileId: null,
    progress: 0,
    estimatedTime: null
  }),

  actions: {
    async checkActiveTranscription() {
      if (!import.meta.client) return;

      try {
        const response = await $fetch<{
          fileId: string;
          isProcessing: boolean;
          progress: number;
          estimatedTime: number | null;
        }>('/api/transcription/status');

        if (response.isProcessing) {
          this.isProcessing = true;
          this.currentFileId = response.fileId;
          this.progress = response.progress || 0;
          this.estimatedTime = response.estimatedTime || null;
        }
      } catch (error) {
        console.error('Failed to check transcription status:', error);
      }
    },

    startTranscription(fileId: string, estimatedTime?: number) {
      this.isProcessing = true;
      this.currentFileId = fileId;
      this.progress = 0;
      this.estimatedTime = estimatedTime || null;
    },

    updateProgress(progress: number, estimatedTime?: number) {
      this.progress = progress;
      if (estimatedTime) {
        this.estimatedTime = estimatedTime;
      }
    },

    completeTranscription() {
      this.isProcessing = false;
      this.currentFileId = null;
      this.progress = 100;
      this.estimatedTime = null;
    },

    cancelTranscription() {
      this.isProcessing = false;
      this.currentFileId = null;
      this.progress = 0;
      this.estimatedTime = null;
    }
  }
});
